<?php $__env->startSection('title', 'Submit Content'); ?>

<?php $__env->startSection('content'); ?>

<?php echo $__env->make('pages.submit-content.components.header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<?php echo $__env->make('pages.submit-content.components.toolbar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>



<?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'uploads.not_submitted')): ?>
<div class="transition-opacity duration-700 <?php echo e($activeTab != 'not_submitted' ? 'hidden' : ''); ?>" id="tab_draft">
<?php echo $__env->make('pages.submit-content.tabs.draft', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
</div>
<?php endif; ?>
<?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'uploads.pending')): ?>
<div class="transition-opacity duration-700 <?php echo e($activeTab != 'pending' ? 'hidden' : ''); ?>" id="tab_pending">
<?php echo $__env->make('pages.submit-content.tabs.pending', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
</div>
<?php endif; ?>

<?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'uploads.rejected')): ?>
<div class="transition-opacity duration-700 <?php echo e($activeTab != 'rejected' ? 'hidden' : ''); ?>" id="tab_rejected">
<?php echo $__env->make('pages.submit-content.tabs.rejected', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
</div>
<?php endif; ?>
<?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'uploads.approved')): ?>
<div class="transition-opacity duration-700 <?php echo e($activeTab != 'approved' ? 'hidden' : ''); ?>" id="tab_approved">
<?php echo $__env->make('pages.submit-content.tabs.approved', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
</div>
<?php endif; ?>
<?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'uploads.published')): ?>
<div class="transition-opacity duration-700 <?php echo e($activeTab != 'published' ? 'hidden' : ''); ?>" id="tab_published">
<?php echo $__env->make('pages.submit-content.tabs.published', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
</div>
<?php endif; ?>



<?php echo $__env->make('pages.submit-content.dropzone', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>



<?php echo $__env->make('pages.submit-content.view-doc', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>



<div id="form-template-container" style="display: none;">
  <?php echo $__env->make('pages.submit-content.form', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
</div>
<div id="show-template-container" style="display: none;">
  <?php echo $__env->make('pages.submit-content.show', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
</div>
<div id="empty-template-container" style="display: none;">
  <?php echo $__env->make('pages.submit-content.empty', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<?php echo $__env->make('pages.submit-content.components.scripts', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<?php echo $__env->make('pages.submit-content.components.main-scripts', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<?php echo $__env->make('pages.submit-content.components.content-scripts', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<?php echo $__env->make('pages.submit-content.components.form-scripts', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<?php echo $__env->make('pages.submit-content.components.review-scripts', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php echo $__env->make('pages.submit-content.components.delete-scripts', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.partials.main', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\grafindo-media-library\resources\views/pages/submit-content/index.blade.php ENDPATH**/ ?>