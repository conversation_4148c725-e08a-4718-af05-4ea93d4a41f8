<div class="modal" data-modal="true" id="search-modal">
  <div class="modal-content max-w-[600px] top-[10%]">
   <div class="modal-header">
    <h3 class="modal-title text-xl">Advanced Search</h3>
    <button class="btn btn-xs btn-icon btn-light" data-modal-dismiss="true">
     <i class="ki-outline ki-cross">
     </i>
    </button>
   </div>
   <div class="modal-body">
    <form id="form-advanced-search">

      <div class="mb-4">
        <label for="media-type" class="text-sm">Tipe Media</label>
        <select class="select mt-1" id="media_type" name="media_type">
          <option value="">Semua Media</option>
          <?php if(isset($explorePermissions['canView']['photo']) && $explorePermissions['canView']['photo']): ?>
          <option value="Photo" <?php echo e(request('media_type') == "Photo" ? 'selected' : ''); ?>>Photo</option>
          <?php endif; ?>
          <?php if(isset($explorePermissions['canView']['illustration']) && $explorePermissions['canView']['illustration']): ?>
          <option value="Illustration" <?php echo e(request('media_type') == "Illustration" ? 'selected' : ''); ?>>Illustration</option>
          <?php endif; ?>
          <?php if(isset($explorePermissions['canView']['video']) && $explorePermissions['canView']['video']): ?>
          <option value="Video" <?php echo e(request('media_type') == 'Video' ? 'selected' : ''); ?>>Video</option>
          <?php endif; ?>
          <?php if(isset($explorePermissions['canView']['audio']) && $explorePermissions['canView']['audio']): ?>
          <option value="Audio" <?php echo e(request('media_type') == "Audio" ? 'selected' : ''); ?>>Audio</option>
          <?php endif; ?>
          <?php if(isset($explorePermissions['canView']['document']) && $explorePermissions['canView']['document']): ?>
          <option value="Document" <?php echo e(request('media_type') == "Document" ? 'selected' : ''); ?>>Document</option>
          <?php endif; ?>
        </select>
      </div>

      <div class="mb-4">
        <label for="category" class="text-sm">Kategori 1 - Umum</label>
        <select class="select mt-1" id="category" name="category_id">
          <option value="">Semua Category</option>
          <?php $__currentLoopData = $general_categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
          <option value="<?php echo e($item->id); ?>" <?php echo e(request('category_id') == $item->id ? 'selected' : ''); ?>><?php echo e($item->name); ?></option>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
      </div>

      <div class="mb-4">
        <label for="category_2" class="text-sm">Kategori 2 - Mata Pelajaran</label>
        <select class="select mt-1" id="category_2" name="category_2_id">
          <option value="">Semua Category</option>
          <?php $__currentLoopData = $subject_categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
          <option value="<?php echo e($item->id); ?>" <?php echo e(request('category_2_id') == $item->id ? 'selected' : ''); ?>><?php echo e($item->name); ?></option>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
      </div>

      <div class="mb-4 flex flex-col">
        <label for="keywords" class="text-sm mb-3">Kata Kunci</label>
        <select id="keywords" class="form-control" multiple="multiple" style="width: 100%" name="keywords[]">
          <?php $__currentLoopData = $keywords; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
          <option value="<?php echo e($item->name); ?>" <?php echo e(in_array($item->name, (array) request('keywords', [])) ? 'selected' : ''); ?>><?php echo e($item->name); ?></option>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
      </div>

      <div class="mb-4">
        <label for="orientation" class="text-sm">Orientasi</label>
        <select class="select mt-1" id="orientation" name="orientation">
          <option value="">Pilih Orientasi</option>
          <option value="Landscape" <?php echo e(request('orientation') == "Landscape" ? 'selected' : ''); ?>>Landscape</option>
          <option value="Portrait" <?php echo e(request('orientation') == "Portrait" ? 'selected' : ''); ?>>Portrait</option>
        </select>
      </div>

      <div class="mb-4">
        <label for="orientation" class="text-sm">Warna</label>
        <div class="grid grid-cols-3 lg:grid-cols-6 gap-5 mt-1">
          <label class="form-label flex items-center gap-2.5 text-nowrap">
          <input class="checkbox" name="color[]" type="checkbox" value="red" <?php echo e(in_array('red', request('color', [])) ? 'checked' : ''); ?>/>
          Merah
          </label>
          <label class="form-label flex items-center gap-2.5 text-nowrap">
          <input class="checkbox" name="color[]" type="checkbox" value="yellow" <?php echo e(in_array('yellow', request('color', [])) ? 'checked' : ''); ?>/>
          Kuning
          </label>
          <label class="form-label flex items-center gap-2.5 text-nowrap">
          <input class="checkbox" name="color[]" type="checkbox" value="green" <?php echo e(in_array('green', request('color', [])) ? 'checked' : ''); ?>/>
          Hijau
          </label>
          <label class="form-label flex items-center gap-2.5 text-nowrap">
          <input class="checkbox" name="color[]" type="checkbox" value="blue" <?php echo e(in_array('blue', request('color', [])) ? 'checked' : ''); ?>/>
          Biru
          </label>
          <label class="form-label flex items-center gap-2.5 text-nowrap">
          <input class="checkbox" name="color[]" type="checkbox" value="black" <?php echo e(in_array('black', request('color', [])) ? 'checked' : ''); ?>/>
          Hitam
          </label>
          <label class="form-label flex items-center gap-2.5 text-nowrap">
          <input class="checkbox" name="color[]" type="checkbox" value="white" <?php echo e(in_array('white', request('color', [])) ? 'checked' : ''); ?>/>
          Putih
          </label>
        </div>
      </div>

    </form>
   </div>
   <div class="modal-footer justify-end">
    <div class="flex gap-2">
     <button class="btn btn-danger btn-outline" id="btn-reset-advanced-search">
      Reset
     </button>
     <button class="btn btn-primary" id="btn-advanced-search">
      Search
     </button>
    </div>
   </div>
  </div>
 </div><?php /**PATH C:\laragon\www\grafindo-media-library\resources\views/pages/explore/advanced-search.blade.php ENDPATH**/ ?>