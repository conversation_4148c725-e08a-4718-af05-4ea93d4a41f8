<?php

namespace App\Http\Controllers;

use App\Services\ExploreService;
use App\Services\DocumentConversionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class ExploreController extends Controller
{
    protected $exploreService;
    protected $documentConversionService;

    protected $page = "Explore";

    public function __construct(ExploreService $exploreService, DocumentConversionService $documentConversionService)
    {
        $this->exploreService = $exploreService;
        $this->documentConversionService = $documentConversionService;
    }

    public function index(Request $request)
    {
        try {
            // Get user permissions for the explore section
            $user = auth()->user();
            $role_id = $user ? $user->role_id : null;

            $explorePermissions = [
                'canView' => [
                    'photo' => $user && $user->hasPermissions('explores.photo', $role_id),
                    'illustration' => $user && $user->hasPermissions('explores.illustration', $role_id),
                    'video' => $user && $user->hasPermissions('explores.video', $role_id),
                    'audio' => $user && $user->hasPermissions('explores.audio', $role_id),
                    'document' => $user && $user->hasPermissions('explores.document', $role_id),
                ],
                'canDownload' => [
                    'photo' => $user && $user->hasPermissions('download.explores.photo', $role_id),
                    'illustration' => $user && $user->hasPermissions('download.explores.illustration', $role_id),
                    'video' => $user && $user->hasPermissions('download.explores.video', $role_id),
                    'audio' => $user && $user->hasPermissions('download.explores.audio', $role_id),
                    'document' => $user && $user->hasPermissions('download.explores.document', $role_id),
                ],
                'canAddToCollection' => [
                    'photo' => $user && $user->hasPermissions('collections.explores.photo', $role_id),
                    'illustration' => $user && $user->hasPermissions('collections.explores.illustration', $role_id),
                    'video' => $user && $user->hasPermissions('collections.explores.video', $role_id),
                    'audio' => $user && $user->hasPermissions('collections.explores.audio', $role_id),
                    'document' => $user && $user->hasPermissions('collections.explores.document', $role_id),
                ],
                'canDelete' => [
                    'photo' => $user && $user->hasPermissions('delete.explores.photo', $role_id),
                    'illustration' => $user && $user->hasPermissions('delete.explores.illustration', $role_id),
                    'video' => $user && $user->hasPermissions('delete.explores.video', $role_id),
                    'audio' => $user && $user->hasPermissions('delete.explores.audio', $role_id),
                    'document' => $user && $user->hasPermissions('delete.explores.document', $role_id),
                ],
                'canBulkDelete' => [
                    'photo' => $user && $user->hasPermissions('bulk delete.explores.photo', $role_id),
                    'illustration' => $user && $user->hasPermissions('bulk delete.explores.illustration', $role_id),
                    'video' => $user && $user->hasPermissions('bulk delete.explores.video', $role_id),
                    'audio' => $user && $user->hasPermissions('bulk delete.explores.audio', $role_id),
                    'document' => $user && $user->hasPermissions('bulk delete.explores.document', $role_id),
                ],
            ];

            // Get allowed media types based on permissions
            $allowedMediaTypes = [];
            if ($explorePermissions['canView']['photo']) $allowedMediaTypes[] = 'Photo';
            if ($explorePermissions['canView']['illustration']) $allowedMediaTypes[] = 'Illustration';
            if ($explorePermissions['canView']['video']) $allowedMediaTypes[] = 'Video';
            if ($explorePermissions['canView']['audio']) $allowedMediaTypes[] = 'Audio';
            if ($explorePermissions['canView']['document']) $allowedMediaTypes[] = 'Document';

            // If no media types are allowed, return empty data
            if (empty($allowedMediaTypes)) {
                $data = [
                    'page' => $this->page,
                    'categories' => $this->exploreService->getCategory('all'),
                    'general_categories' => $this->exploreService->getCategory('all', 'general'),
                    'subject_categories' => $this->exploreService->getCategory('all', 'subject'),
                    'keywords' => $this->exploreService->getKeywords('all'),
                    'data' => collect([]), // Empty collection
                    'explorePermissions' => $explorePermissions
                ];
                return view('pages.explore.index', $data);
            }

            // If a specific media type is requested, check if it's allowed
            $requestData = $request->all();
            if (isset($requestData['media_type']) && !empty($requestData['media_type'])) {
                if (!in_array($requestData['media_type'], $allowedMediaTypes)) {
                    // If requested media type is not allowed, redirect to explore without media type filter
                    return redirect()->route('explore');
                }
            } else {
                // If no specific media type is requested (All Media), add allowed media types filter
                $requestData['allowed_media_types'] = $allowedMediaTypes;
            }

            $data = [
                'page' => $this->page,
                'categories' => $this->exploreService->getCategory('all'),
                'general_categories' => $this->exploreService->getCategory('all', 'general'),
                'subject_categories' => $this->exploreService->getCategory('all', 'subject'),
                'keywords' => $this->exploreService->getKeywords('all'),
                'data' => $this->exploreService->getPublishedContent($requestData),
                'explorePermissions' => $explorePermissions
            ];

            // if ($request->ajax()) {
            //     return response()->json([
            //         'data' => view('pages.explore.content', ['data' => $data['data']])->render(),
            //         'next_page' => $data['data']->nextPageUrl()
            //     ]);
            // }

            return view('pages.explore.index', $data);
        } catch (\Exception $e) {
            return redirect()->back()->with([
                'type' => 'error',
                'message' => config('messages.error.get')
            ], $e->getCode() ?: 500);
        }
    }

    public function download(Request $request, $id)
    {
        try {
            $ip = $request->ip();
            $path = $this->exploreService->download($id, $ip);

            return response()->download($path);
        } catch (\Exception $e) {
            return redirect()->back()->with([
                'type' => 'error',
                'message' => $e->getCode() == 404 ? $e->getMessage() : config('messages.error.download')
            ], $e->getCode() ?: 500);
        }
    }

    public function downloads_count($id)
    {
        try {
            $data = $this->exploreService->getOneContent($id);

            return response()->json([
                'status' => 'success',
                'message' => config('messages.success.download'),
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function favorite(Request $request)
    {
        try {
            $data = $this->exploreService->favorite($request->all());

            return response()->json([
                'status' => 'success',
                'message' => config('messages.success.'.$request->type),
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function destroy($id)
    {
        try {
            // Check if user has permission to delete this content
            $content = $this->exploreService->getOneContent($id);
            $mediaType = strtolower($content->media_type);

            $user = auth()->user();
            $role_id = $user ? $user->role_id : null;

            if (!$user || !$user->hasPermissions("delete.explores.{$mediaType}", $role_id)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'You do not have permission to delete this content'
                ], 403);
            }

            $this->exploreService->delete($id);

            return response()->json([
                'status' => 'success',
                'message' => config('messages.success.archive'),
                'data' => null
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage() ?: config('messages.error.delete')
            ], $e->getCode() ?: 500);
        }
    }

    public function bulkDestroy(Request $request)
    {
        try {
            // Check if user has permission to bulk delete content
            $user = auth()->user();
            $role_id = $user ? $user->role_id : null;

            // Get the media types of the content to be deleted
            $contentItems = [];
            foreach ($request->ids as $id) {
                $content = $this->exploreService->getOneContent($id);
                $mediaType = strtolower($content->media_type);

                // Check permission for each item
                if (!$user || !$user->hasPermissions("bulk delete.explores.{$mediaType}", $role_id)) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'You do not have permission to bulk delete some of the selected content'
                    ], 403);
                }

                $contentItems[] = $id;
            }

            $this->exploreService->bulkDelete($contentItems);

            return response()->json([
                'status' => 'success',
                'message' => config('messages.success.bulk_archive'),
                'data' => null
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage() ?: config('messages.error.bulk_delete')
            ], $e->getCode() ?: 500);
        }
    }

    /**
     * Convert a DOC/DOCX file to PDF for preview
     *
     * @param int $id Content ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function convertDocumentForPreview($id)
    {
        try {
            // Get the content item
            $content = $this->exploreService->getOneContent($id);

            if (!$content) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Content not found'
                ], 404);
            }

            // Check if it's a document
            if ($content->media_type !== 'Document') {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Content is not a document'
                ], 400);
            }

            // Check file extension
            $extension = pathinfo($content->file_path, PATHINFO_EXTENSION);
            if (!in_array(strtolower($extension), ['doc', 'docx'])) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'File is not a DOC/DOCX document'
                ], 400);
            }

            // Check if PDF version already exists
            $pdfPath = $this->documentConversionService->getPdfVersion($content->file_path);

            // If not, convert it
            if (!$pdfPath) {
                $pdfPath = $this->documentConversionService->convertToPdf($content->file_path);

                if (!$pdfPath) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Failed to convert document to PDF'
                    ], 500);
                }
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Document converted successfully',
                'data' => [
                    'pdf_path' => $pdfPath
                ]
            ], 200);
        } catch (\Exception $e) {
            Log::error('Document conversion error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Error converting document: ' . $e->getMessage()
            ], 500);
        }
    }
}
