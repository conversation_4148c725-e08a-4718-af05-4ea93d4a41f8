
<div class="details-view-header">
  <div class="details-view-row">
    <div class="details-view-cell" style="width: 40%;">Name</div>
    <div class="details-view-cell" style="width: 20%;">Date Uploaded</div>
    <div class="details-view-cell" style="width: 15%;">Type</div>
    <div class="details-view-cell" style="width: 15%;">Creator</div>
    <div class="details-view-cell" style="width: 10%;">Actions</div>
  </div>
</div>


<?php $__currentLoopData = $data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
<div class="details-view-row" data-id="<?php echo e($item->id); ?>">
  <div class="details-view-cell">
    <div class="file-info-cell">
      <div class="file-icon">
        <?php if($item->media_type == "Audio"): ?>
          <img src="<?php echo e(asset('assets/thumbnail/thumbnail_audio.svg')); ?>" alt="<?php echo e($item->filename); ?>" class="p-1 bg-white">
        <?php elseif($item->media_type == "Document"): ?>
          <img src="<?php echo e(asset('assets/'.$item->thumbnail_path)); ?>" alt="<?php echo e($item->filename); ?>">
        <?php else: ?>
          <img src="<?php echo e(Storage::url($item->thumbnail_path)); ?>" alt="<?php echo e($item->filename); ?>">
        <?php endif; ?>
      </div>
      <div class="file-name"><?php echo e($item->title); ?></div>
    </div>
  </div>
  <div class="details-view-cell"><?php echo e($item->created_at->format('d/m/Y H:i')); ?></div>
  <div class="details-view-cell"><?php echo e($item->media_type); ?> File</div>
  <div class="details-view-cell"><?php echo e($item->user->name ?? 'Unknown'); ?>/GMP</div>
  <div class="details-view-cell">
    <div class="actions-cell">
      <?php
        $mediaType = strtolower($item->media_type);
        $canDownload = isset($explorePermissions['canDownload'][$mediaType]) ? $explorePermissions['canDownload'][$mediaType] : false;
      ?>
      <?php if($canDownload): ?>
      <a href="/explore/download/<?php echo e($item->id); ?>" class="action-btn btn-download" title="Download" data-media-type="<?php echo e($mediaType); ?>">
        <i class="ki-filled ki-arrow-down text-gray-600"></i>
      </a>
      <?php endif; ?>

      <?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'collection.explores')): ?>
      <button class="action-btn btn-collection" title="Add to Collection" data-id="<?php echo e($item->id); ?>">
        <i class="ki-outline ki-add-folder text-gray-600"></i>
      </button>
      <?php endif; ?>

      

      <button class="action-btn" data-modal-toggle="#detail-modal-<?php echo e($item->id); ?>" title="View Details">
        <i class="ki-filled ki-eye text-gray-600"></i>
      </button>

      <?php
        $mediaType = strtolower($item->media_type);
        $canDelete = isset($explorePermissions['canDelete'][$mediaType]) ? $explorePermissions['canDelete'][$mediaType] : false;
      ?>

      <?php if($canDelete): ?>
      <button type="button" class="action-btn btn-delete" title="Delete" data-id="<?php echo e($item->id); ?>" data-media-type="<?php echo e($mediaType); ?>" onclick="event.stopPropagation(); deleteContent(event, '<?php echo e($item->id); ?>', '<?php echo e($mediaType); ?>')">
        <i class="ki-outline ki-trash text-gray-600"></i>
      </button>
      <?php endif; ?>
    </div>
  </div>
</div>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php /**PATH C:\laragon\www\grafindo-media-library\resources\views/pages/explore/details-view.blade.php ENDPATH**/ ?>