<?php $__env->startSection('title', 'Archive'); ?>

<?php
use Illuminate\Support\Facades\Storage;
?>

<?php $__env->startPush('styles'); ?>
<link href="<?php echo e(url('')); ?>/assets/css/explore-table-view.css" rel="stylesheet">
<style>
/* Additional styles for archive table */
.actions-column {
  width: 15%;
  text-align: center;
}

.actions-cell {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
}

.actions-cell .btn {
  margin: 0;
}

/* Adjust column widths for archive table */
.table-view .name-column {
  width: 35%;
}

.table-view .date-column {
  width: 20%;
}

.table-view .type-column {
  width: 15%;
}

.table-view .creator-column {
  width: 15%;
}

/* Pagination styling integration */
.archive-pagination {
  border-top: 1px solid #e5e7eb;
  padding-top: 1rem;
  margin-top: 1rem;
}

/* Ensure pagination text matches table styling */
.archive-pagination p {
  color: #6b7280;
  font-size: 0.875rem;
}

/* Center modals properly */
#confirmRestoreModal .modal-content,
#confirmDeleteModal .modal-content {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  margin: 0 !important;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>

<?php if(session('debug')): ?>
    <div class="alert alert-info">
        Debug Mode: View is being rendered
    </div>
<?php endif; ?>
<!--begin::Container-->
<div class="container-fluid">
    <!--begin::Card-->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Archived Items</h3>
        </div>
        <div class="card-body">
            
            <div class="table-view-container" style="display: block;">
                <table class="table-view">
                    <thead>
                        <tr>
                            <th class="name-column">Name</th>
                            <th class="date-column">Archived At</th>
                            <th class="type-column">Type</th>
                            <th class="creator-column">Author</th>
                            <th class="actions-column">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $archivedItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="table-row" data-id="<?php echo e($item->id); ?>">
                            <td class="name-column">
                                <div class="file-info">
                                    <div class="file-icon">
                                        <?php
                                            $iconClass = '';
                                            $iconColor = '';
                                            $filename = $item->original_filename ?? $item->filename ?? '';

                                            if($item->media_type == "Document") {
                                                if(str_ends_with(strtolower($filename), '.pdf')) {
                                                    $iconClass = 'fa-solid fa-file-pdf';
                                                    $iconColor = '#e53e3e'; // Merah untuk PDF
                                                } elseif(str_ends_with(strtolower($filename), '.doc') || str_ends_with(strtolower($filename), '.docx')) {
                                                    $iconClass = 'fa-solid fa-file-word';
                                                    $iconColor = '#3182ce'; // Biru untuk Word
                                                } elseif(str_ends_with(strtolower($filename), '.xls') || str_ends_with(strtolower($filename), '.xlsx')) {
                                                    $iconClass = 'fa-solid fa-file-excel';
                                                    $iconColor = '#38a169'; // Hijau untuk Excel
                                                } elseif(str_ends_with(strtolower($filename), '.ppt') || str_ends_with(strtolower($filename), '.pptx')) {
                                                    $iconClass = 'fa-solid fa-file-powerpoint';
                                                    $iconColor = '#dd6b20'; // Oranye untuk PowerPoint
                                                } elseif(str_ends_with(strtolower($filename), '.txt')) {
                                                    $iconClass = 'fa-solid fa-file-lines';
                                                    $iconColor = '#718096'; // Abu-abu untuk TXT
                                                } else {
                                                    $iconClass = 'fa-solid fa-file';
                                                    $iconColor = '#718096'; // Abu-abu untuk file lainnya
                                                }
                                            } elseif($item->media_type == "Audio") {
                                                $iconClass = 'fa-solid fa-file-audio';
                                                $iconColor = '#805ad5'; // Ungu untuk Audio
                                            } elseif($item->media_type == "Video") {
                                                $iconClass = 'fa-solid fa-file-video';
                                                $iconColor = '#dd6b20'; // Oranye untuk Video
                                            } else {
                                                $iconClass = 'fa-solid fa-file-image';
                                                $iconColor = '#38a169'; // Hijau untuk Image
                                            }
                                        ?>
                                        <i class="<?php echo e($iconClass); ?>" style="color: <?php echo e($iconColor); ?> !important;"></i>
                                    </div>
                                    <span class="file-name"><?php echo e($item->original_filename ?? $item->filename ?? 'Unknown'); ?></span>
                                </div>
                            </td>
                            <td class="date-column"><?php echo e($item->archived_at ? \Carbon\Carbon::parse($item->archived_at)->setTimezone('Asia/Jakarta')->format('d/m/Y H:i') : 'N/A'); ?></td>
                            <td class="type-column"><?php echo e($item->media_type ?? 'N/A'); ?> File</td>
                            <td class="creator-column"><?php echo e($item->author->name ?? 'Unknown'); ?></td>
                            <td class="actions-column">
                                <div class="actions-cell">
                                    <?php if($item->file_path): ?>
                                    <a href="<?php echo e(Storage::url($item->file_path)); ?>"
                                       class="btn btn-icon btn-bg-light btn-active-color-primary btn-sm"
                                       data-tooltip="Preview"
                                       target="_blank">
                                        <i class="ki-duotone ki-eye fs-2">
                                            <span class="path1"></span>
                                            <span class="path2"></span>
                                            <span class="path3"></span>
                                        </i>
                                    </a>
                                    <?php endif; ?>
                                    <button class="btn btn-icon btn-bg-light btn-active-color-success btn-sm btn-restore"
                                            data-tooltip="Restore"
                                            data-id="<?php echo e($item->id); ?>"
                                            data-title="<?php echo e($item->original_filename ?? $item->filename ?? 'Unknown'); ?>">
                                        <i class="ki-duotone ki-arrow-rotate-left fs-2">
                                            <span class="path1"></span>
                                            <span class="path2"></span>
                                        </i>
                                    </button>
                                    <button class="btn btn-icon btn-bg-light btn-active-color-danger btn-sm btn-delete"
                                            data-tooltip="Delete Permanently"
                                            data-id="<?php echo e($item->id); ?>"
                                            data-title="<?php echo e($item->original_filename ?? $item->filename ?? 'Unknown'); ?>">
                                        <i class="ki-duotone ki-trash fs-2">
                                            <span class="path1"></span>
                                            <span class="path2"></span>
                                            <span class="path3"></span>
                                            <span class="path4"></span>
                                            <span class="path5"></span>
                                        </i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="5" class="text-center py-10">
                                <div class="text-muted fw-semibold">No archived items found</div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            <!--end::Table-->

            
            <?php if($archivedItems->hasPages()): ?>
            <div class="my-5 archive-pagination">
                <?php echo e($archivedItems->links('vendor.pagination.tailwind')); ?>

            </div>
            <?php endif; ?>

        </div>
        <!--end::Card body-->
    </div>
    <!--end::Card-->
</div>
<!--end::Container-->

<!--begin::Modal - Confirm Restore-->
<div class="modal" data-modal="true" id="confirmRestoreModal">
    <div class="modal-content max-w-[500px]">
        <div class="modal-header">
            <h3 class="modal-title">Confirm Restore</h3>
            <button class="btn btn-xs btn-icon btn-light" data-modal-dismiss="true">
                <i class="ki-outline ki-cross"></i>
            </button>
        </div>
        <div class="modal-body">
            <p>Are you sure you want to restore this item?</p>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-light" data-modal-dismiss="true">Cancel</button>
            <button type="button" class="btn btn-primary" id="confirmRestore">Restore</button>
        </div>
    </div>
</div>
<!--end::Modal - Confirm Restore-->

<!--begin::Modal - Confirm Delete-->
<div class="modal" data-modal="true" id="confirmDeleteModal">
    <div class="modal-content max-w-[500px]">
        <div class="modal-header">
            <h3 class="modal-title text-danger">Confirm Permanent Deletion</h3>
            <button class="btn btn-xs btn-icon btn-light" data-modal-dismiss="true">
                <i class="ki-outline ki-cross"></i>
            </button>
        </div>
        <div class="modal-body">
            <p>This action cannot be undone. Are you sure you want to permanently delete this item?</p>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-light" data-modal-dismiss="true">No</button>
            <button type="button" class="btn btn-danger" id="confirmDelete">Yes</button>
        </div>
    </div>
</div>
<!--end::Modal - Confirm Delete-->

<!--begin::Javascript-->
<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle restore button click
        document.querySelectorAll('.btn-restore').forEach(button => {
            button.addEventListener('click', function() {
                const itemId = this.getAttribute('data-id');
                const itemTitle = this.getAttribute('data-title');

                // Get modal element and show it
                const modalElement = document.getElementById('confirmRestoreModal');
                const modal = KTModal.getInstance(modalElement) || new KTModal(modalElement);
                const confirmButton = document.getElementById('confirmRestore');

                // Store the item ID in the confirm button
                confirmButton.setAttribute('data-id', itemId);

                // Show the modal
                modal.show();
            });
        });

        // Handle delete button click
        document.querySelectorAll('.btn-delete').forEach(button => {
            button.addEventListener('click', function() {
                const itemId = this.getAttribute('data-id');
                const itemTitle = this.getAttribute('data-title');

                // Get modal element and show it
                const modalElement = document.getElementById('confirmDeleteModal');
                const modal = KTModal.getInstance(modalElement) || new KTModal(modalElement);
                const confirmButton = document.getElementById('confirmDelete');

                // Store the item ID in the confirm button
                confirmButton.setAttribute('data-id', itemId);

                // Show the modal
                modal.show();
            });
        });

        // Handle confirm restore
        document.getElementById('confirmRestore')?.addEventListener('click', function() {
            const itemId = this.getAttribute('data-id');
            const button = this;
            
            // Show loading state
            button.setAttribute('data-kt-indicator', 'on');
            button.disabled = true;
            
            // Make AJAX request to restore the item
            fetch(`/archive/restore/${itemId}`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Hide loading state
                button.removeAttribute('data-kt-indicator');
                button.disabled = false;
                
                // Close the modal
                const modalElement = document.getElementById('confirmRestoreModal');
                const modal = KTModal.getInstance(modalElement);
                if (modal) modal.hide();

                if (data.success) {
                    // Show success message
                    myToast('success', data.message || 'Item has been restored successfully.');
                    
                    // Remove the row from the table
                    const row = document.querySelector(`tr[data-id="${itemId}"]`);
                    if (row) {
                        row.remove();
                    }
                    
                    // If no more items, show empty state
                    if (document.querySelectorAll('tbody tr[data-id]').length === 0) {
                        window.location.reload();
                    }
                } else {
                    myToast('error', data.message || 'Failed to restore item.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                button.removeAttribute('data-kt-indicator');
                button.disabled = false;
                myToast('error', 'An error occurred while restoring the item.');
            });
        });

        // Handle confirm delete
        document.getElementById('confirmDelete')?.addEventListener('click', function() {
            const itemId = this.getAttribute('data-id');
            const button = this;
            
            // Show loading state
            button.setAttribute('data-kt-indicator', 'on');
            button.disabled = true;
            
            // Make AJAX request to delete the item
            fetch(`/archive/delete/${itemId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Hide loading state
                button.removeAttribute('data-kt-indicator');
                button.disabled = false;
                
                // Close the modal
                const modalElement = document.getElementById('confirmDeleteModal');
                const modal = KTModal.getInstance(modalElement);
                if (modal) modal.hide();

                if (data.success) {
                    // Show success message
                    myToast('success', data.message || 'Item has been permanently deleted.');
                    
                    // Remove the row from the table
                    const row = document.querySelector(`tr[data-id="${itemId}"]`);
                    if (row) {
                        row.remove();
                    }
                    
                    // If no more items, show empty state
                    if (document.querySelectorAll('tbody tr[data-id]').length === 0) {
                        window.location.reload();
                    }
                } else {
                    myToast('error', data.message || 'Failed to delete item.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                button.removeAttribute('data-kt-indicator');
                button.disabled = false;
                myToast('error', 'An error occurred while deleting the item.');
            });
        });
    });
</script>
<?php $__env->stopPush(); ?>
<!--end::Javascript-->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.partials.main', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\grafindo-media-library\resources\views/pages/archive/index.blade.php ENDPATH**/ ?>