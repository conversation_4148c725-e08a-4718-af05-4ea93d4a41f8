<!-- Sidebar -->
<div class="fixed top-0 left-0 bottom-0 z-50 w-[250px] bg-[#f5f7fa] text-gray-700 border-r border-gray-300 overflow-y-auto transform -translate-x-full lg:translate-x-0 transition-transform duration-300" id="sidebar" style="border-right-color: #d1d5db !important;">
  <!-- Logo -->
  <div class="flex items-center gap-2 px-5 pt-5 pb-4 border-b border-gray-300" style="border-bottom-width: 1px !important; border-bottom-color: #e5e7eb !important;">
    <a href="<?php echo e(route('explore')); ?>" class="flex items-center gap-2">
      <img class="dark:hidden min-h-[34px]" src="<?php echo e(url('')); ?>/assets/media/app/mini-logo-circle.svg"/>
      <img class="hidden dark:inline-block min-h-[34px]" src="<?php echo e(url('')); ?>/assets/media/app/mini-logo-circle-dark.svg"/>
      <h3 class="text-gray-800 text-lg font-medium mb-1">
        Media Library
      </h3>
    </a>
  </div>

  <!-- Menu -->
  <div class="menu flex flex-col p-3 gap-1 mt-1" data-menu="true">
    <!-- Explore - Only visible if user has at least one view permission -->
    <?php
      $user = auth()->user();
      $role_id = $user ? $user->role_id : null;
      $mediaTypes = [
        'Photo' => 'explores.photo',
        'Illustration' => 'explores.illustration',
        'Video' => 'explores.video',
        'Audio' => 'explores.audio',
        'Document' => 'explores.document',
        'ETC' => 'explores.etc'
      ];

      // Check if user has at least one view permission
      $hasAnyViewPermission = false;
      foreach ($mediaTypes as $permission) {
        if ($user && $user->hasPermissions($permission, $role_id)) {
          $hasAnyViewPermission = true;
          break;
        }
      }
    ?>

    <?php if($hasAnyViewPermission): ?>
    <div class="menu-item <?php if(Request::segment(1) == 'explore'): ?> active <?php endif; ?>">
      <a class="menu-link py-2 px-4 rounded-md relative <?php if(Request::segment(1) == 'explore'): ?> bg-gray-100 <?php endif; ?>" href="javascript:void(0);" data-toggle-submenu="explore-submenu">
        <span class="menu-icon mr-3">
          <i class="ki-outline ki-compass"></i>
        </span>
        <span class="menu-title font-medium text-gray-700 text-sm">
          Explore
        </span>
        <span class="menu-arrow">
          <i class="fas fa-chevron-down submenu-arrow"></i>
        </span>
      </a>
    </div>
    <?php endif; ?>

    <!-- Explore Submenu Items -->
    <div id="explore-submenu" class="pl-10 submenu-container <?php if(Request::segment(1) != 'explore'): ?> hidden <?php endif; ?>">

      <?php if($hasAnyViewPermission): ?>
      <div class="menu-item">
        <a class="menu-link py-2 px-4 rounded-md <?php if(Request::segment(1) == 'explore' && !Request::has('media_type')): ?> bg-gray-100 <?php endif; ?>" href="<?php echo e(route('explore')); ?>">
          <span class="menu-title font-medium text-gray-700 text-sm">
            All Media
          </span>
        </a>
      </div>
      <?php endif; ?>

      <?php $__currentLoopData = $mediaTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mediaType => $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php if($user && $user->hasPermissions($permission, $role_id) || $mediaType === 'ETC'): ?>
        <div class="menu-item">
          <?php if($mediaType === 'ETC'): ?>
          <div class="menu-link py-2 px-4 rounded-md">
            <span class="menu-title font-medium text-gray-700 text-sm">
              ETC
            </span>
          </div>
          <?php else: ?>
          <a class="menu-link py-2 px-4 rounded-md <?php if(Request::query('media_type') == $mediaType): ?> bg-gray-100 <?php endif; ?>" href="<?php echo e(route('explore')); ?>?media_type=<?php echo e($mediaType); ?>">
            <span class="menu-title font-medium text-gray-700 text-sm">
              <?php echo e($mediaType); ?>

            </span>
          </a>
          <?php endif; ?>
        </div>
        <?php endif; ?>
      <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>

    <!-- Submit Content -->
    <?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'uploads.upload_now', 'uploads.not_submitted', 'uploads.pending', 'uploads.rejected', 'uploads.approved', 'uploads.published')): ?>
    <div class="menu-item <?php if(Request::segment(1) == 'submit-content'): ?> active <?php endif; ?>">
      <a class="menu-link py-2 px-4 rounded-md relative <?php if(Request::segment(1) == 'submit-content'): ?> bg-gray-100 <?php endif; ?>" href="javascript:void(0);" data-toggle-submenu="submit-content-submenu">
        <span class="menu-icon mr-3">
          <i class="ki-outline ki-document"></i>
        </span>
        <span class="menu-title font-medium text-gray-700 text-sm">
          Submit Content
        </span>
        <span class="menu-arrow">
          <i class="fas fa-chevron-down submenu-arrow"></i>
        </span>
      </a>
    </div>

    <!-- Submit Content Submenu Items -->
    <div id="submit-content-submenu" class="pl-10 submenu-container <?php if(Request::segment(1) != 'submit-content'): ?> hidden <?php endif; ?>">
      <?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'uploads.not_submitted')): ?>
      <div class="menu-item">
        <a class="menu-link py-2 px-4 rounded-md <?php if(Request::query('tab') == 'not_submitted' || !Request::has('tab')): ?> bg-gray-100 <?php endif; ?>" href="<?php echo e(route('content')); ?>?tab=not_submitted">
          <span class="menu-title font-medium text-gray-700 text-sm">
            Not Submitted
          </span>
        </a>
      </div>
      <?php endif; ?>

      <?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'uploads.pending')): ?>
      <div class="menu-item">
        <a class="menu-link py-2 px-4 rounded-md <?php if(Request::query('tab') == 'pending'): ?> bg-gray-100 <?php endif; ?>" href="<?php echo e(route('content')); ?>?tab=pending">
          <span class="menu-title font-medium text-gray-700 text-sm">
            Pending
          </span>
        </a>
      </div>
      <?php endif; ?>

      <?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'uploads.rejected')): ?>
      <div class="menu-item">
        <a class="menu-link py-2 px-4 rounded-md <?php if(Request::query('tab') == 'rejected'): ?> bg-gray-100 <?php endif; ?>" href="<?php echo e(route('content')); ?>?tab=rejected">
          <span class="menu-title font-medium text-gray-700 text-sm">
            Rejected
          </span>
        </a>
      </div>
      <?php endif; ?>

      <?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'uploads.approved')): ?>
      <div class="menu-item">
        <a class="menu-link py-2 px-4 rounded-md <?php if(Request::query('tab') == 'approved'): ?> bg-gray-100 <?php endif; ?>" href="<?php echo e(route('content')); ?>?tab=approved">
          <span class="menu-title font-medium text-gray-700 text-sm">
            Approved
          </span>
        </a>
      </div>
      <?php endif; ?>

      <?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'uploads.published')): ?>
      <div class="menu-item">
        <a class="menu-link py-2 px-4 rounded-md <?php if(Request::query('tab') == 'published'): ?> bg-gray-100 <?php endif; ?>" href="<?php echo e(route('content')); ?>?tab=published">
          <span class="menu-title font-medium text-gray-700 text-sm">
            Published
          </span>
        </a>
      </div>
      <?php endif; ?>
    </div>
    <?php endif; ?>

    <!-- Review Content -->
    <?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'reviews')): ?>
    <div class="menu-item <?php if(Request::segment(1) == 'review-content'): ?> active <?php endif; ?>">
      <a class="menu-link py-2 px-4 rounded-md relative <?php if(Request::segment(1) == 'review-content'): ?> bg-gray-100 <?php endif; ?>" href="javascript:void(0);" data-toggle-submenu="review-content-submenu">
        <span class="menu-icon mr-3">
          <i class="far fa-check-circle"></i>
        </span>
        <span class="menu-title font-medium text-gray-700 text-sm">
          Review Content
        </span>
        <span class="menu-arrow">
          <i class="fas fa-chevron-down submenu-arrow"></i>
        </span>
      </a>
    </div>

    <!-- Review Content Submenu Items -->
    <div id="review-content-submenu" class="pl-10 submenu-container <?php if(Request::segment(1) != 'review-content'): ?> hidden <?php endif; ?>">
      <?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'approve.reviews')): ?>
      <div class="menu-item">
        <a class="menu-link py-2 px-4 rounded-md <?php if(Request::query('tab') == 'pending' || !Request::has('tab')): ?> bg-gray-100 <?php endif; ?>" href="<?php echo e(route('review')); ?>?tab=pending">
          <span class="menu-title font-medium text-gray-700 text-sm">
            Pending
          </span>
        </a>
      </div>
      <?php endif; ?>

      <?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'reject.reviews')): ?>
      <div class="menu-item">
        <a class="menu-link py-2 px-4 rounded-md <?php if(Request::query('tab') == 'rejected'): ?> bg-gray-100 <?php endif; ?>" href="<?php echo e(route('review')); ?>?tab=rejected">
          <span class="menu-title font-medium text-gray-700 text-sm">
            Rejected
          </span>
        </a>
      </div>
      <?php endif; ?>

      <?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'approve.reviews')): ?>
      <div class="menu-item">
        <a class="menu-link py-2 px-4 rounded-md <?php if(Request::query('tab') == 'approved'): ?> bg-gray-100 <?php endif; ?>" href="<?php echo e(route('review')); ?>?tab=approved">
          <span class="menu-title font-medium text-gray-700 text-sm">
            Approved
          </span>
        </a>
      </div>
      <?php endif; ?>

      <?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'publish.reviews')): ?>
      <div class="menu-item">
        <a class="menu-link py-2 px-4 rounded-md <?php if(Request::query('tab') == 'published'): ?> bg-gray-100 <?php endif; ?>" href="<?php echo e(route('review')); ?>?tab=published">
          <span class="menu-title font-medium text-gray-700 text-sm">
            Published
          </span>
        </a>
      </div>
      <?php endif; ?>
    </div>
    <?php endif; ?>

    <!-- References -->
    <?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'category1', 'category2')): ?>
    <div class="menu-item <?php if(Request::segment(1) == 'reference'): ?> active <?php endif; ?>">
      <a class="menu-link py-2 px-4 rounded-md relative <?php if(Request::segment(1) == 'reference'): ?> bg-gray-100 <?php endif; ?>" href="javascript:void(0);" data-toggle-submenu="references-submenu">
        <span class="menu-icon mr-3">
          <i class="ki-outline ki-book"></i>
        </span>
        <span class="menu-title font-medium text-gray-700 text-sm">
          References
        </span>
        <span class="menu-arrow">
          <i class="fas fa-chevron-down submenu-arrow"></i>
        </span>
      </a>
    </div>

    <!-- References Submenu Items -->
    <div id="references-submenu" class="pl-10 submenu-container <?php if(Request::segment(1) != 'reference'): ?> hidden <?php endif; ?>">
      <?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'category1')): ?>
      <div class="menu-item">
        <a class="menu-link py-2 px-4 rounded-md <?php if(Request::query('type') == 'general' || !Request::has('type')): ?> bg-gray-100 <?php endif; ?>" href="<?php echo e(route('categories', ['type' => 'general'])); ?>">
          <span class="menu-title font-medium text-gray-700 text-sm">
            Category 1 (Umum)
          </span>
        </a>
      </div>
      <?php endif; ?>

      <?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'category2')): ?>
      <div class="menu-item">
        <a class="menu-link py-2 px-4 rounded-md <?php if(Request::query('type') == 'subject'): ?> bg-gray-100 <?php endif; ?>" href="<?php echo e(route('categories', ['type' => 'subject'])); ?>">
          <span class="menu-title font-medium text-gray-700 text-sm">
            Category 2 (Mata Pelajaran)
          </span>
        </a>
      </div>
      <?php endif; ?>
    </div>
    <?php endif; ?>

    <!-- Manage -->
    <?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'users', 'roles')): ?>
    <div class="menu-item <?php if(Request::segment(1) == 'manage'): ?> active <?php endif; ?>">
      <a class="menu-link py-2 px-4 rounded-md relative <?php if(Request::segment(1) == 'manage'): ?> bg-gray-100 <?php endif; ?>" href="javascript:void(0);" data-toggle-submenu="manage-submenu">
        <span class="menu-icon mr-3">
          <i class="ki-outline ki-setting-2"></i>
        </span>
        <span class="menu-title font-medium text-gray-700 text-sm">
          Manage
        </span>
        <span class="menu-arrow">
          <i class="fas fa-chevron-down submenu-arrow"></i>
        </span>
      </a>
    </div>

    <!-- Manage Submenu Items -->
    <div id="manage-submenu" class="pl-10 submenu-container <?php if(Request::segment(1) != 'manage'): ?> hidden <?php endif; ?>">
      <?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'users')): ?>
      <div class="menu-item">
        <a class="menu-link py-2 px-4 rounded-md <?php if(Request::segment(2) == 'account'): ?> bg-gray-100 <?php endif; ?>" href="<?php echo e(route('account')); ?>">
          <span class="menu-title font-medium text-gray-700 text-sm">
            Account
          </span>
        </a>
      </div>
      <?php endif; ?>

      <?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'roles')): ?>
      <div class="menu-item">
        <a class="menu-link py-2 px-4 rounded-md <?php if(Request::segment(2) == 'roles'): ?> bg-gray-100 <?php endif; ?>" href="<?php echo e(route('roles')); ?>">
          <span class="menu-title font-medium text-gray-700 text-sm">
            Role & Permissions
          </span>
        </a>
      </div>
      <?php endif; ?>
    </div>
    <?php endif; ?>

    <!-- Archive -->
    <?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'archive')): ?>
    <div class="menu-item <?php if(Request::segment(1) == 'archive'): ?> active <?php endif; ?>">
      <a class="menu-link py-2 px-4 rounded-md relative <?php if(Request::segment(1) == 'archive'): ?> bg-gray-100 <?php endif; ?>" href="<?php echo e(route('archive')); ?>">
        <span class="menu-icon mr-3">
          <i class="ki-outline ki-archive"></i>
        </span>
        <span class="menu-title font-medium text-gray-700 text-sm">
          Archive
        </span>
      </a>
    </div>
    <?php endif; ?>

    <!-- My Profile -->
    <?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'profiles')): ?>
    <div class="menu-item <?php if(Request::segment(1) == 'profiles'): ?> active <?php endif; ?>">
      <a class="menu-link py-2 px-4 rounded-md relative <?php if(Request::segment(1) == 'profiles'): ?> bg-gray-100 <?php endif; ?>" href="javascript:void(0);" data-toggle-submenu="my-profile-submenu">
        <span class="menu-icon mr-3">
          <i class="ki-outline ki-user"></i>
        </span>
        <span class="menu-title font-medium text-gray-700 text-sm">
          My Profile
        </span>
        <span class="menu-arrow">
          <i class="fas fa-chevron-down submenu-arrow"></i>
        </span>
      </a>
    </div>

    <!-- My Profile Submenu -->
    <div id="my-profile-submenu" class="pl-10 submenu-container <?php if(Request::segment(1) != 'profiles'): ?> hidden <?php endif; ?>">
      <!-- Profile Info -->
      <div class="menu-item">
        <a class="menu-link py-2 px-4 rounded-md <?php if(Request::query('tab') == 'my-profile' || !Request::has('tab')): ?> bg-gray-100 <?php endif; ?>" href="/profiles?tab=my-profile">
          <span class="menu-title font-medium text-gray-700 text-sm">
            Profile Info
          </span>
        </a>
      </div>

      <!-- My Works -->
      <?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'works')): ?>
      <div class="menu-item">
        <a class="menu-link py-2 px-4 rounded-md <?php if(Request::query('tab') == 'works'): ?> bg-gray-100 <?php endif; ?>" href="/profiles?tab=works">
          <span class="menu-title font-medium text-gray-700 text-sm">
            My Works
          </span>
        </a>
      </div>
      <?php endif; ?>

      <!-- My Collections -->
      <?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'collections')): ?>
      <div class="menu-item">
        <a class="menu-link py-2 px-4 rounded-md <?php if(Request::query('tab') == 'collections'): ?> bg-gray-100 <?php endif; ?>" href="/profiles?tab=collections">
          <span class="menu-title font-medium text-gray-700 text-sm">
            My Collections
          </span>
        </a>
      </div>
      <?php endif; ?>
    </div>
    <?php endif; ?>
  </div>
</div>
<!-- End of Sidebar -->

<!-- Mobile Sidebar Overlay -->
<div class="fixed inset-0 bg-black/50 z-10 hidden" id="sidebar-overlay"></div>

<style>
  /* Sidebar light theme */
  #sidebar {
    background-color: #f5f7fa;
    color: #495057;
  }

  /* Logo section styling with customizable border */
  #sidebar > div:first-child {
    border-bottom: 1px solid #e5e7eb !important;
    position: relative;
    margin-bottom: 0;
  }

  /* Adjust the Media Library text container */
  #sidebar > div:first-child h3 {
    margin-bottom: 0.25rem;
  }

  #sidebar .menu-link {
    color: #495057;
  }

  #sidebar .menu-link:hover {
    background-color: #e9ecef !important; /* Light gray on hover */
  }

  #sidebar .menu-link.bg-gray-50,
  #sidebar .menu-link.bg-gray-100,
  #sidebar .menu-link.dark\:bg-coal-300,
  #sidebar .menu-link.dark\:bg-coal-400,
  #sidebar .menu-link.bg-gray-900 {
    background-color: #e9ecef !important; /* Light gray for active items */
    border-left: 3px solid #6c757d !important; /* Darker gray border for better visibility */
    border-top: 1px solid #dee2e6 !important; /* Top border */
    border-bottom: 1px solid #dee2e6 !important; /* Bottom border */
    padding-left: calc(1rem - 3px) !important; /* Adjust padding to prevent content shift */
  }

  #sidebar .menu-title {
    color: #495057 !important; /* Dark gray text */
  }

  /* Ensure all submenu text is the same color */
  #sidebar [id$="-submenu"] .menu-title {
    color: #495057 !important; /* Dark gray text */
  }

  /* Submenu styling */
  #sidebar [id$="-submenu"] .menu-link.bg-gray-50,
  #sidebar [id$="-submenu"] .menu-link.bg-gray-800,
  #sidebar [id$="-submenu"] .menu-link.dark\:bg-coal-300 {
    background-color: #e9ecef !important; /* Light gray for active submenu items */
    border-left: 3px solid #6c757d !important; /* Darker gray border for better visibility */
    border-top: 1px solid #dee2e6 !important; /* Top border */
    border-bottom: 1px solid #dee2e6 !important; /* Bottom border */
    padding-left: calc(1rem - 3px) !important; /* Adjust padding */
  }

  #sidebar [id$="-submenu"] .menu-link:hover {
    background-color: #dee2e6 !important; /* Slightly darker gray on hover */
  }

  /* Submenu container without connecting lines */
  .submenu-container {
    position: relative;
    margin-left: 12px;
    padding-left: 20px;
  }

  .submenu-container .menu-item {
    position: relative;
  }

  /* Ensure icons are visible */
  .menu-icon i {
    display: inline-block;
    font-size: 18px;
    width: 24px;
    height: 24px;
    text-align: center;
    line-height: 24px;
  }

  /* Keenicons outline */
  .ki-outline {
    font-size: 18px;
    color: #495057 !important;
    width: 20px;
    text-align: center;
    display: inline-block !important;
    visibility: visible !important;
  }

  /* Font Awesome icons */
  .far, .fas, .fa-solid, .fa-regular {
    font-size: 16px;
    color: #495057 !important;
    width: 20px;
    text-align: center;
    display: inline-block !important;
    visibility: visible !important;
  }

  /* Menu arrow styling */
  .menu-arrow {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease;
    position: absolute;
    right: 15px;
  }

  .menu-arrow i {
    font-size: 12px;
    color: #6c757d !important;
    display: inline-block !important;
    visibility: visible !important;
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize arrow states based on visible submenus
    document.querySelectorAll('[id$="-submenu"]').forEach(submenu => {
      if (!submenu.classList.contains('hidden')) {
        const submenuId = submenu.id;
        const toggle = document.querySelector(`[data-toggle-submenu="${submenuId}"]`);
        if (toggle) {
          const arrow = toggle.querySelector('.submenu-arrow');
          if (arrow) {
            arrow.classList.remove('fa-chevron-down');
            arrow.classList.add('fa-chevron-up');
          }
        }
      }
    });
    // Mobile sidebar toggle
    const sidebarToggle = document.querySelector('[data-drawer-toggle="#sidebar"]');
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('sidebar-overlay');

    if (sidebarToggle && sidebar && overlay) {
      sidebarToggle.addEventListener('click', function() {
        sidebar.classList.toggle('translate-x-0');
        sidebar.classList.toggle('-translate-x-full');
        overlay.classList.toggle('hidden');
      });

      overlay.addEventListener('click', function() {
        sidebar.classList.remove('translate-x-0');
        sidebar.classList.add('-translate-x-full');
        overlay.classList.add('hidden');
      });
    }

    // Submenu toggle functionality
    const submenuToggles = document.querySelectorAll('[data-toggle-submenu]');

    submenuToggles.forEach(toggle => {
      toggle.addEventListener('click', function() {
        const submenuId = this.getAttribute('data-toggle-submenu');
        const submenu = document.getElementById(submenuId);
        const arrow = this.querySelector('.submenu-arrow');

        // Check if this submenu is already visible
        const isVisible = !submenu.classList.contains('hidden');

        // First, reset all arrows to down position
        document.querySelectorAll('.submenu-arrow').forEach(arrow => {
          arrow.classList.remove('fa-chevron-up');
          arrow.classList.add('fa-chevron-down');
        });

        // First, hide all submenus
        document.querySelectorAll('[id$="-submenu"]').forEach(menu => {
          if (menu.id !== submenuId) {
            menu.classList.add('hidden');
          }
        });

        // Toggle the clicked submenu and update arrow
        if (isVisible) {
          submenu.classList.add('hidden');
          arrow.classList.remove('fa-chevron-up');
          arrow.classList.add('fa-chevron-down');
        } else {
          submenu.classList.remove('hidden');
          arrow.classList.remove('fa-chevron-down');
          arrow.classList.add('fa-chevron-up');
        }
      });
    });
  });
</script>
<?php /**PATH C:\laragon\www\grafindo-media-library\resources\views/layouts/partials/sidebar.blade.php ENDPATH**/ ?>