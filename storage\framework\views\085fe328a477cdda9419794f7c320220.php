<?php $__env->startSection('title', 'Login'); ?>
    
<?php $__env->startSection('content'); ?>
    <!-- Page -->
  <style>
    .page-bg {
       background-image: url('assets/media/images/2600x1200/bg-10.png');
     }
     .dark .page-bg {
       background-image: url('assets/media/images/2600x1200/bg-10-dark.png');
     }
   </style>
   <?php echo $__env->make('components.toast', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

   <div class="flex items-center justify-center grow bg-center bg-no-repeat page-bg">
    <div class="card max-w-[370px] w-full">
     <form action="<?php echo e(route('login')); ?>" method="POST" class="card-body flex flex-col gap-5 p-10">
      <?php echo csrf_field(); ?>
      <div class="text-center mb-2.5">
       <h3 class="text-lg font-medium text-gray-900 leading-none mb-2.5">
        Sign in
       </h3>
       
      </div>
      <div class="flex flex-col gap-1">
       <input name="username" class="input" placeholder="Username" type="text" value="" required/>
      </div>
      <div class="flex flex-col gap-1">
       <div class="flex items-center justify-between gap-1">
        <label class="form-label font-normal text-gray-900">
        </label>
       </div>
       <div class="input" data-toggle-password="true">
        <input name="password" class="pass-input" placeholder="Enter Password" type="password" value="" required/>
        <button class="btn btn-icon btn-eye" type="button">
         <i class="ki-filled ki-eye eye-icon text-gray-500">
         </i>
        </button>
       </div>
       
      </div>
      <button class="btn btn-primary flex justify-center grow" type="submit">
       Sign In
      </button>
     </form>
    </div>
   </div>
   <!-- End of Page -->
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
  const eyeIcon = $('.eye-icon');
  const passInput = $('.pass-input');

  $('.btn-eye').on('click', function(){
    if(eyeIcon.hasClass('ki-eye')){
      passInput.attr('type', 'text')
      eyeIcon.removeClass('ki-eye').addClass('ki-eye-slash')
    }else{
      passInput.attr('type', 'password')
      eyeIcon.addClass('ki-eye').removeClass('ki-eye-slash')
    }
  });
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.guest', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\grafindo-media-library\resources\views/pages/auth/login.blade.php ENDPATH**/ ?>