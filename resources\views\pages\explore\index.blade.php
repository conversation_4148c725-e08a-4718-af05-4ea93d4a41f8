@extends('layouts.partials.main')

@section('title', 'Explore')

@push('styles')
<link href="{{ url('') }}/assets/css/explore-gallery.css" rel="stylesheet">
<link href="{{ url('') }}/assets/css/explore-details-view.css" rel="stylesheet">
<link href="{{ url('') }}/assets/css/explore-table-view.css" rel="stylesheet">
<link href="{{ url('') }}/assets/css/pdf-viewer.css" rel="stylesheet">
<style>
  /* All checkbox styling has been removed as we're now using the same HTML structure as the submit-content section */

  /* Ensure buttons and checkboxes are above the overlay */
  .gallery-item .btn-delete,
  .gallery-item .btn-collection,
  .gallery-item .btn-download,
  .gallery-item .download-btn,
  .gallery-item .checkbox-container {
    position: relative;
    z-index: 30;
  }

  /* Download button styling */
  .download-btn {
    min-width: 40px;
    height: 40px;
    transition: all 0.3s ease;
  }

  /* Responsive styles for download button */
  @media (max-width: 640px) {
    .download-btn {
      width: 40px;
      height: 40px;
      padding: 8px;
    }
    .download-text {
      display: none;
    }
  }

  @media (min-width: 641px) {
    .download-btn {
      min-width: 40px;
      padding: 8px 16px;
    }
  }

  /* Prevent the modal toggle from capturing clicks on buttons */
  .gallery-item [data-modal-toggle] {
    pointer-events: none;
  }

  /* But allow clicks on the image itself */
  .gallery-item img {
    pointer-events: auto;
  }

  /* Hide PDF viewer toolbar */
  .pdf-viewer-no-toolbar {
    border: none;
  }

  /* View mode dropdown styling */
  #view-mode-menu {
    box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1);
    border-radius: 0.475rem;
    padding: 0.625rem 0;
  }

  #view-mode-menu .dropdown-item {
    transition: background-color 0.2s ease;
  }

  #view-mode-menu .dropdown-item:hover {
    background-color: #f8f9fa;
  }

  #view-mode-menu .dropdown-item.active {
    background-color: #f1f3f5;
  }
</style>
@endpush

@section('content')
<div class="container-fixed mb-8">
  <div class="flex flex-col items-center justify-center gap-2.5 flex-wrap">
    <h1 class="text-5xl">Smart creativity, Smart Work</h1>
    <h4 class="text-lg">Everything you need, from stock images and videos to a complete set of creative resources.</h4>

    <div class="flex items-center justify-center w-full">
      <form class="lg:w-[45%] my-5">
        <div class="input-group w-full">
          <select class="select lg:w-[35%] hidden lg:flex" name="media_type">
            <option value="">
            All Media
            </option>
            @if(isset($explorePermissions['canView']['photo']) && $explorePermissions['canView']['photo'])
            <option value="Photo" {{ request('media_type') == "Photo" ? 'selected' : '' }}>
            Photo
            </option>
            @endif
            @if(isset($explorePermissions['canView']['illustration']) && $explorePermissions['canView']['illustration'])
            <option value="Illustration" {{ request('media_type') == "Illustration" ? 'selected' : '' }}>
            Illustration
            </option>
            @endif
            @if(isset($explorePermissions['canView']['video']) && $explorePermissions['canView']['video'])
            <option value="Video" {{ request('media_type') == "Video" ? 'selected' : '' }}>
            Video
            </option>
            @endif
            @if(isset($explorePermissions['canView']['audio']) && $explorePermissions['canView']['audio'])
            <option value="Audio" {{ request('media_type') == "Audio" ? 'selected' : '' }}>
            Audio
            </option>
            @endif
            @if(isset($explorePermissions['canView']['document']) && $explorePermissions['canView']['document'])
            <option value="Document" {{ request('media_type') == "Document" ? 'selected' : '' }}>
            Document
            </option>
            @endif
          </select>
          <input class="input ml-2" placeholder="Search anything here..." type="text" name="search" value="{{ request('search') }}"/>
          <button type="submit" class="btn btn-primary">
            <i class="ki-filled ki-magnifier"></i>
            <span class="hidden lg:flex">Search</span>
          </button>
        </div>
      </form>
      <button class="btn btn-outline btn-primary ml-2" data-modal-toggle="#search-modal">
        <i class="ki-filled ki-setting-4"></i>
      </button>
      <div class="dropdown ml-2" id="view-mode-dropdown" data-dropdown="true" data-dropdown-trigger="click">
        <button class="btn btn-outline btn-primary dropdown-toggle" type="button">
          <i class="ki-filled ki-eye"></i>
        </button>
        <div class="dropdown-content min-w-[150px]" id="view-mode-menu">
          <div class="dropdown-item cursor-pointer view-mode-option py-2 px-3" data-mode="large-icon">
            <div class="flex items-center gap-3">
              <i class="ki-filled ki-grid-2 text-gray-600"></i>
              <span class="font-medium">Large Icon</span>
            </div>
          </div>
          <div class="dropdown-item cursor-pointer view-mode-option py-2 px-3" data-mode="table">
            <div class="flex items-center gap-3">
              <i class="ki-filled ki-row-horizontal text-gray-600"></i>
              <span class="font-medium">Details</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    @include('pages.explore.advanced-search')
  </div>
</div>
{{-- End of sticky filters --}}
<!-- Container -->
@if ($data->isEmpty())
  @include('components.empty-content')
@endif

<div class="container-fixed">
  <div class="flex justify-end items-center my-5">

    <!-- Bulk Delete Button - Initially Hidden -->
    <div id="bulk-actions" class="hidden">
      <button id="btn-bulk-delete" class="btn btn-danger">
        <i class="ki-outline ki-trash"></i>
        <span>Delete Selected</span>
      </button>
    </div>
  </div>

  <div id="content-container" class="large-icon-view">
    @include('pages.explore.content')
    @include('pages.explore.details-view')
    @include('pages.explore.table-view')
  </div>

  <div class="my-5">
    {{ $data->links('vendor.pagination.tailwind') }}
  </div>

  {{-- <div class="text-center loading hidden">
    <div role="status">
      <svg aria-hidden="true" class="inline w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-gray-600 dark:fill-gray-300" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
          <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/>
      </svg>
      <span class="sr-only">Loading...</span>
    </div>
  </div> --}}

  @include('pages.explore.collection-drawer')

</div>
@endsection

@push('scripts')
<script src="{{ url('') }}/assets/js/explore-grid.js"></script>
<script src="{{ url('') }}/assets/js/pdf-viewer.js"></script>
<script src="{{ url('') }}/assets/js/doc-preview.js"></script>
<script>
  // Pass permissions to JavaScript
  window.explorePermissions = @json($explorePermissions ?? []);

  // Global delete content function - defined outside document.ready to be available immediately
  function deleteContent(e, contentId, mediaType) {
    e.preventDefault();
    e.stopPropagation();

    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      customClass:{
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-light',
      },
      confirmButtonText: "Yes, delete it!"
    }).then((result) => {
      if (result.isConfirmed) {
        $.ajax({
          url: "/explore/delete/" + contentId,
          method: "DELETE",
          data: {
            '_token': "{{ csrf_token() }}"
          },
          success: function(res) {
            myToast(res.status, res.message);
            // Remove the item from the view
            $(`[data-id="${contentId}"]`).fadeOut(300, function() {
              $(this).remove();
              // Reinitialize grid if in large icon mode
              if ($('#content-container').hasClass('large-icon-view')) {
                new ExploreGrid('#content-container', '.gallery-item', {
                  rowTargetHeight: 250,
                  minImagesPerRow: 5,
                  maxImagesPerRow: 5,
                  itemGap: 3
                });
              }
            });
          },
          error: function(err) {
            let res = err.responseJSON;
            myToast('error', res.message || 'An error occurred while deleting the item');
          }
        });
      }
    });
  }
</script>
<script>
  $(document).ready(function(){
    // View mode handling
    let currentViewMode = localStorage.getItem('exploreViewMode') || 'large-icon';

    // Initialize view mode
    setViewMode(currentViewMode);

    // View mode toggle
    $('.view-mode-option').on('click', function() {
      const mode = $(this).data('mode');
      setViewMode(mode);
      localStorage.setItem('exploreViewMode', mode);
      // Hide dropdown after selection
      const dropdown = KTDropdown.getInstance(document.getElementById('view-mode-dropdown'));
      if (dropdown) {
        dropdown.hide();
      }
    });

    function setViewMode(mode) {
      const container = $('#content-container');

      // Update dropdown button icon
      if (mode === 'large-icon') {
        $('#view-mode-dropdown .dropdown-toggle i').removeClass().addClass('ki-filled ki-grid-2');
      } else {
        $('#view-mode-dropdown .dropdown-toggle i').removeClass().addClass('ki-filled ki-row-horizontal');
      }

      // Update active class in dropdown menu
      $('.view-mode-option').removeClass('active');
      $(`.view-mode-option[data-mode="${mode}"]`).addClass('active');

      // Update container class
      container.removeClass('large-icon-view details-view table-view');
      container.addClass(mode + '-view');

      // Reinitialize grid if in large icon mode
      if (mode === 'large-icon') {
        new ExploreGrid('#content-container', '.gallery-item', {
          rowTargetHeight: 250,
          minImagesPerRow: 5,
          maxImagesPerRow: 5,
          itemGap: 3
        });
      }
    }
    const drawerEl = document.querySelector('#collection_drawer');
    const drawer = KTDrawer.getInstance(drawerEl);
    const formSearch = $("#form-advanced-search");
    let next_page = "{{ $data->nextPageUrl() }}";
    let content_id;

    // let $paginationContainer = $("#pagination-container");
    // $paginationContainer.empty();
    // console.log(data)
    // if (data.links) {
    //   let active = `class="z-10 page-link flex items-center justify-center px-3 h-8 leading-tight text-blue-600 border border-blue-300 bg-blue-50 hover:bg-blue-100 hover:text-blue-700 dark:border-gray-700 dark:bg-gray-700 dark:text-white"`;
    //   let inactive = `class="page-link flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white"`;

    //   data.links.forEach(link => {
    //     let navigation = link.label;
    //     let pageLink;

    //     if(navigation.toLowerCase().includes("previous")){
    //       pageLink = `
    //         <li>
    //           <a href="#" data-url="${link.url}" class="page-link flex items-center justify-center px-3 h-8 ms-0 leading-tight text-gray-500 bg-white border border-e-0 border-gray-300 rounded-s-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
    //             <span class="sr-only">${link.label}</span>
    //             <svg class="w-2.5 h-2.5 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
    //               <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 1 1 5l4 4"/>
    //             </svg>
    //           </a>
    //         </li>
    //       `;
    //     }else if(navigation.toLowerCase().includes("next")){
    //       pageLink = `
    //         <li>
    //           <a href="#" data-url="${link.url}" class="page-link flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 rounded-e-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
    //             <span class="sr-only">${link.label}</span>
    //             <svg class="w-2.5 h-2.5 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
    //               <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
    //             </svg>
    //           </a>
    //         </li>
    //       `;
    //     }else{
    //       pageLink = `
    //         <li>
    //           <a href="#" data-url="${link.url}" ${link.active ? active+' aria-current="page"' : inactive}>${link.label}</a>
    //         </li>
    //       `;
    //     }

    //     $paginationContainer.append(pageLink);
    //   });

    //   $(".page-link").on("click", function (e) {
    //       e.preventDefault();
    //       let url = $(this).data("url");
    //       detailContent.html(emptyContent);
    //       getContent(status, filter, authorId, url);
    //   });
    // }

    // if(next_page == "") $(".show-more").hide();
    // else $(".show-more").show();

    // $(".show-more").on("click", function(e){
    //   if(next_page){
    //     loadMoreContent();
    //   }
    // })

    // function loadMoreContent() {
    //   $(".loading").show();
    //   $(".show-more").hide();
    //   $.get(next_page, function(response) {
    //       if (response.data) {
    //         $("#content-container").append(response.data);
    //         next_page = response.next_page;
    //       } else {
    //         next_page = null;
    //       }
    //       if(next_page) $(".show-more").show();
    //       $(".loading").hide();
    //   });
    // }

    function getCollection() {
      $("#collection-list").empty();
      $.ajax({
        url: "{{ route('get-collections') }}",
        method: "GET",
        beforeSend: function() {
          $(".collection-loading").show()
        },
        success: function(res) {
          $(".collection-loading").hide()

          let data = res.data
          data.forEach(item=> {
            $("#collection-list").append(`
              <div class="flex justify-between gap-2">
                <button class="flex flex-row hover:bg-gray-200 rounded-md w-full h-10 p-2 btn-item-collection" data-id="${item.id}">
                  <i class="ki-filled ki-right flex items-center"></i>
                  <i class="hidden ki-filled ki-check text-success flex items-center"></i>
                  <span class="menu-title ml-2 flex items-center">${item.name}</span>
                </button>
                <a href="" class="flex items-center"><i class="ki-outline ki-exit-right-corner hover:text-blue-500"></i></a>
              </div>
            `)
          })
        },
        error: function(err) {
          let res = err.responseJSON
          myToast(res.status, res.message)
        }
      });
    }

    $(".btn-collection").on("click", function(e){
      e.stopPropagation();
      let mediaType = $(this).data("media-type");

      // Check if user has collection permission for this media type
      if (window.explorePermissions &&
          window.explorePermissions.canAddToCollection &&
          window.explorePermissions.canAddToCollection[mediaType]) {

        drawer.toggle();
        getCollection();
        $("#btn-save-collection").hide();
        $(".collection-footer").hide();
        $("#btn-create-collection").show();
        let card = $(this).closest(".group");
        content_id = card.data("id");
      } else {
        myToast('error', 'You do not have permission to add this item to collections');
      }
    })

    $(document).on("click", ".btn-item-collection", function(e){
      let isSelected = $(this).hasClass("selected");

      $(".btn-item-collection").removeClass("selected bg-gray-200");
      $(".btn-item-collection .ki-right").show();
      $(".btn-item-collection .ki-check").addClass("hidden").removeClass("flex");

      if (isSelected) {
          $("#btn-save-collection").hide();
          $("#btn-create-collection").show();
      } else {
          $(this).addClass("selected bg-gray-200");
          $(this).find(".ki-right").hide();
          $(this).find(".ki-check").addClass("flex").removeClass("hidden");

          $("#btn-save-collection").show();
          $("#btn-create-collection").hide();
      }
    })

    $(".btn-batal-koleksi").on("click", function() {
      let selectedItem = $(".btn-item-collection.selected");

      if (selectedItem.length) {
          let iconRight = selectedItem.find(".ki-right");
          let iconCheck = selectedItem.find(".ki-check");

          selectedItem.removeClass("selected bg-gray-200");
          iconRight.show();
          iconCheck.addClass("hidden").removeClass("flex");
      }

      $("#btn-save-collection").hide()
      $('#btn-create-collection').show()
    })

    $(".btn-simpan-koleksi").on("click", function() {
      let isSelected = $(".btn-item-collection.selected");
      let collectionId = isSelected.data("id")

      $.ajax({
        url: "{{ route('add-item-collection') }}",
        method: "POST",
        data: {
          '_token': "{{ csrf_token() }}",
          'collection_id': collectionId,
          'content_id': content_id,
        },
        beforeSend: function() {
          $('.collection-footer').hide()
          $("#btn-save-collection").hide()
          $(".collection-footer-loading").show()
        },
        success: function(res) {
          $(".collection-footer-loading").hide()
          $("#btn-save-collection").hide()
          $("#btn-create-collection").show()
          getCollection()
          myToast(res.status, res.message)
        },
        error: function(err) {
          let res = err.responseJSON
          let status = 'error'
          console.log(res)
          myToast(status, res.message)
          $(".collection-footer-loading").hide()
        }
      });
    })

    $("#btn-create-collection").on("click", function() {
      $('.collection-footer').show()
      $("#collection_input").val("")
      $(this).hide();
    })

    $("#btn-cancel-create-collection").on("click", function() {
      $('.collection-footer').hide()
      $('#btn-create-collection').show()
    })

    $("#collection_input").on("keyup", function() {
      let input = $(this).val().trim()
      if(input === ""){
        $("#btn-store-collection").prop('disabled', true)
      }else{
        $("#btn-store-collection").prop('disabled', false)
      }
    })

    $("#btn-store-collection").on("click", function() {
      let inputName = $("#collection_input").val().trim()

      $.ajax({
        url: "{{ route('store-collection') }}",
        method: "POST",
        data: {
          '_token': '{{ csrf_token() }}',
          'name': inputName
        },
        beforeSend: function() {
          $('.collection-footer').hide()
          $(".collection-footer-loading").show()
        },
        success: function(res) {
          console.log(res)
          $(".collection-footer-loading").hide()
          $('#btn-create-collection').show()
          getCollection()
          myToast(res.status, res.message)
        },
        error: function(err) {
          let res = err.responseJSON
          console.log(res)
          myToast(res.status, res.message)
        }
      });
    })

    function updateFavorite(contentId, type) {
      $.ajax({
        url: 'explore/favorite',
        method: 'PUT',
        data: {
          '_token': "{{ csrf_token() }}",
          'id': contentId,
          'type': type
        },
        success: function (res){
          myToast(res.status, res.message)
        },
        error: function (err){
          let res = err.responseJSON;
          myToast(res.status, res.message)
        }
      });
    }

    $(".btn-favorite").on("click", function(e){
      e.stopPropagation();
      let card = $(this).closest(".group");
      let cardId = card.data("id");
      let buttonFav = $(this);
      let buttonLike = $(".btn-like");
      let type = buttonFav.data("type");

      buttonFav.addClass("scale-125").delay(200).queue(function(next) {
          $(this).removeClass("scale-125");
          next();
      });

      if (type === "like") {
          buttonFav.removeClass("bg-transparent border-white hover:bg-slate-50 hover:bg-opacity-10")
                .addClass("bg-red-500 hover:bg-red-600")
                .data("type", "dislike")
                .find(".tooltip").text("Unfavorite");

          buttonLike.removeClass("btn-light")
                .addClass("btn-danger")
                .data("type", "dislike")
                .find(".text-btn").text("Hapus dari favorit");
      } else {
          buttonFav.removeClass("bg-red-500 hover:bg-red-600")
                .addClass("bg-transparent border-white hover:bg-slate-50 hover:bg-opacity-10")
                .data("type", "like")
                .find(".tooltip").text("Favorite");

          buttonLike.removeClass("btn-danger")
                .addClass("btn-light")
                .data("type", "like")
                .find(".text-btn").text("Tambahkan ke favorit");
      }

      updateFavorite(cardId, type);
    })

    $(".btn-like").on("click", function(e){
      let card = $(this).closest(".group");
      let cardId = card.data("id");
      let buttonLike = $(this);
      let buttonFav = $(".btn-favorite");
      let type = buttonLike.data("type");

      buttonLike.addClass("scale-120").delay(200).queue(function(next) {
          $(this).removeClass("scale-120");
          next();
      });

      if (type === "like") {
          buttonLike.removeClass("btn-light")
                .addClass("btn-danger")
                .data("type", "dislike")
                .find(".text-btn").text("Hapus dari favorit");

          buttonFav.removeClass("bg-transparent border-white hover:bg-slate-50 hover:bg-opacity-10")
                .addClass("bg-red-500 hover:bg-red-600")
                .data("type", "dislike")
                .find(".tooltip").text("Unfavorite");
      } else {
          buttonLike.removeClass("btn-danger")
                .addClass("btn-light")
                .data("type", "like")
                .find(".text-btn").text("Tambahkan ke favorit");

          buttonFav.removeClass("bg-red-500 hover:bg-red-600")
                .addClass("bg-transparent border-white hover:bg-slate-50 hover:bg-opacity-10")
                .data("type", "like")
                .find(".tooltip").text("Favorite");
      }

      updateFavorite(cardId, type);
    })

    // Handle both old and new download buttons
    $(".btn-download, .download-btn").on("click", function(e){
      e.stopPropagation();
      let card = $(this).closest(".group");
      let cardId = card.data("id");
      let mediaType = $(this).data("media-type");

      // Check if user has download permission for this media type
      if (window.explorePermissions &&
          window.explorePermissions.canDownload &&
          window.explorePermissions.canDownload[mediaType]) {

        setTimeout(() => {
          $.ajax({
            url: "explore/downloads-count/"+cardId,
            method: 'GET',
            success: function(res){
              $("#detail-modal-"+cardId).find("#downloads_count").text(` ${res.data.downloads_count}`)
              myToast(res.status, res.message)
            }
          });
        }, 2000);
      } else {
        myToast('error', 'You do not have permission to download this file');
        e.preventDefault();
        return false;
      }
    })

    $("#btn-reset-advanced-search").on("click", function(){
      $("#media_type").val("").change()
      $("#category").val("").change()
      $("#category_2").val("").change()
      $("#keywords").val("").change()
      $("#orientation").val("").change()
      $(".checkbox").prop("checked", false)
    })

    $("#btn-advanced-search").on("click", function(){
      formSearch.submit();
    })

    $('#keywords').select2({
      tokenSeparators: [',', ' '],
      placeholder: "Pilih Kata Kunci",
    });

    // We're now using the global deleteContent function with inline onclick handlers
    // This ensures the confirmation dialog appears properly

    // Bulk delete functionality
    let selectedItems = [];

    // Handle checkbox selection
    $(document).on("change", ".bulk-select-checkbox", function(e) {
      e.preventDefault();
      e.stopPropagation();
      const contentId = $(this).data("id");
      const $checkbox = $(this);
      const $container = $checkbox.closest('.multiple-select');

      if ($(this).is(":checked")) {
        selectedItems.push(contentId);
        $container.find('.unchecked').hide();
        $container.find('.checked').removeClass('hidden');
      } else {
        selectedItems = selectedItems.filter(id => id !== contentId);
        $container.find('.unchecked').show();
        $container.find('.checked').addClass('hidden');
      }

      // Show/hide bulk actions
      if (selectedItems.length > 0) {
        $("#bulk-actions").removeClass("hidden");
      } else {
        $("#bulk-actions").addClass("hidden");
      }
    });

    // Bulk delete button
    $("#btn-bulk-delete").on("click", function() {
      if (selectedItems.length === 0) {
        myToast('error', 'No items selected');
        return;
      }

      Swal.fire({
        title: "Delete selected items?",
        text: `You are about to delete ${selectedItems.length} item(s). This cannot be undone!`,
        icon: "warning",
        showCancelButton: true,
        customClass:{
          confirmButton: 'btn btn-danger',
          cancelButton: 'btn btn-light',
        },
        confirmButtonText: "Yes, delete them!"
      }).then((result) => {
        if (result.isConfirmed) {
          $.ajax({
            url: "/explore/bulk-delete",
            method: "DELETE",
            data: {
              '_token': "{{ csrf_token() }}",
              'ids': selectedItems
            },
            success: function(res) {
              myToast(res.status, res.message);

              // Remove the items from the view
              selectedItems.forEach(id => {
                $(`[data-id="${id}"]`).fadeOut(300, function() {
                  $(this).remove();
                });
              });

              // Reset selection
              selectedItems = [];
              $("#bulk-actions").addClass("hidden");

              // Reinitialize grid if in large icon mode
              if ($('#content-container').hasClass('large-icon-view')) {
                new ExploreGrid('#content-container', '.gallery-item', {
                  rowTargetHeight: 250,
                  minImagesPerRow: 5,
                  maxImagesPerRow: 5,
                  itemGap: 3
                });
              }
            },
            error: function(err) {
              let res = err.responseJSON;
              myToast('error', res.message || 'An error occurred while deleting the items');
            }
          });
        }
      });
    });

    // Table sorting functionality
    $('.sortable').on('click', function() {
      const sortBy = $(this).data('sort');
      const currentSortBy = new URLSearchParams(window.location.search).get('sort_by');
      const currentSortOrder = new URLSearchParams(window.location.search).get('sort_order');

      let newSortOrder = 'asc';

      // If clicking on the same column, toggle the order
      if (currentSortBy === sortBy) {
        newSortOrder = currentSortOrder === 'asc' ? 'desc' : 'asc';
      }

      // Create new URL with sort parameters
      const url = new URL(window.location.href);
      url.searchParams.set('sort_by', sortBy);
      url.searchParams.set('sort_order', newSortOrder);

      // Navigate to the new URL
      window.location.href = url.toString();
    });

  })
</script>
@endpush
