@extends('layouts.partials.main')

@section('title', 'Submit Content')

@section('content')
{{-- Head Content --}}
@include('pages.submit-content.components.header')
{{-- End of head content --}}
@include('pages.submit-content.components.toolbar')


{{-- Submit Tabs --}}
@canAccess('uploads.not_submitted')
<div class="transition-opacity duration-700 {{ $activeTab != 'not_submitted' ? 'hidden' : '' }}" id="tab_draft">
@include('pages.submit-content.tabs.draft')
</div>
@endcanAccess
@canAccess('uploads.pending')
<div class="transition-opacity duration-700 {{ $activeTab != 'pending' ? 'hidden' : '' }}" id="tab_pending">
@include('pages.submit-content.tabs.pending')
</div>
@endcanAccess
{{-- <div class="hidden transition-opacity duration-700" id="tab_reviewed">
@include('pages.submit-content.tabs.reviewed')
</div> --}}
@canAccess('uploads.rejected')
<div class="transition-opacity duration-700 {{ $activeTab != 'rejected' ? 'hidden' : '' }}" id="tab_rejected">
@include('pages.submit-content.tabs.rejected')
</div>
@endcanAccess
@canAccess('uploads.approved')
<div class="transition-opacity duration-700 {{ $activeTab != 'approved' ? 'hidden' : '' }}" id="tab_approved">
@include('pages.submit-content.tabs.approved')
</div>
@endcanAccess
@canAccess('uploads.published')
<div class="transition-opacity duration-700 {{ $activeTab != 'published' ? 'hidden' : '' }}" id="tab_published">
@include('pages.submit-content.tabs.published')
</div>
@endcanAccess
{{-- End of submit tabs --}}

{{-- Upload Drpozone Modal --}}
@include('pages.submit-content.dropzone')
{{-- End of dropzone modal --}}

{{-- Modal View Doc Release --}}
@include('pages.submit-content.view-doc')
{{-- End of view doc release --}}

{{-- Hidden template containers for JavaScript --}}
<div id="form-template-container" style="display: none;">
  @include('pages.submit-content.form')
</div>
<div id="show-template-container" style="display: none;">
  @include('pages.submit-content.show')
</div>
<div id="empty-template-container" style="display: none;">
  @include('pages.submit-content.empty')
</div>
{{-- End of hidden template containers --}}
@endsection

@push('scripts')
@include('pages.submit-content.components.scripts')

@include('pages.submit-content.components.main-scripts')

@include('pages.submit-content.components.content-scripts')

@include('pages.submit-content.components.form-scripts')

@include('pages.submit-content.components.review-scripts')
@include('pages.submit-content.components.delete-scripts')
@endpush