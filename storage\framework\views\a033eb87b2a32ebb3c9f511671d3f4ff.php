
<!DOCTYPE html>
<html class="h-full" data-theme="true" data-theme-mode="light" dir="ltr" lang="en">
 <head><base href="../../../../">
  <title>
   <?php echo $__env->yieldContent('title', 'Grafindo Media Library'); ?>
  </title>
  <meta charset="utf-8"/>
  <meta content="follow, index" name="robots"/>
  <link href="https://127.0.0.1:8001/metronic-tailwind-html/demo8/authentication/classic/sign-in" rel="canonical"/>
  <meta content="width=device-width, initial-scale=1, shrink-to-fit=no" name="viewport"/>
  <meta content="Sign in page using Tailwind CSS" name="description"/>
  <meta content="@keenthemes" name="twitter:site"/>
  <meta content="@keenthemes" name="twitter:creator"/>
  <meta content="summary_large_image" name="twitter:card"/>
  <meta content="Metronic - Tailwind CSS Sign In" name="twitter:title"/>
  <meta content="Sign in page using Tailwind CSS" name="twitter:description"/>
  <meta content="assets/media/app/og-image.png" name="twitter:image"/>
  <meta content="https://127.0.0.1:8001/metronic-tailwind-html/demo8/authentication/classic/sign-in" property="og:url"/>
  <meta content="en_US" property="og:locale"/>
  <meta content="website" property="og:type"/>
  <meta content="@keenthemes" property="og:site_name"/>
  <meta content="Metronic - Tailwind CSS Sign In" property="og:title"/>
  <meta content="Sign in page using Tailwind CSS" property="og:description"/>
  <meta content="assets/media/app/og-image.png" property="og:image"/>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet"/>
  <link href="<?php echo e(url('')); ?>/assets/media/app/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="<?php echo e(url('')); ?>/assets/media/app/favicon-32x32.png" rel="icon" sizes="32x32" type="image/png"/>
  <link href="<?php echo e(url('')); ?>/assets/media/app/favicon-16x16.png" rel="icon" sizes="16x16" type="image/png"/>
  <link href="<?php echo e(url('')); ?>/assets/media/app/favicon.ico" rel="shortcut icon"/>
  <link href="<?php echo e(url('')); ?>/assets/vendors/apexcharts/apexcharts.css" rel="stylesheet"/>
  <link href="<?php echo e(url('')); ?>/assets/vendors/keenicons/styles.bundle.css" rel="stylesheet"/>
  <link href="<?php echo e(url('')); ?>/assets/css/styles.css" rel="stylesheet"/>
  <link href="<?php echo e(url('')); ?>/assets/fontawesome/css/fontawesome.css" rel="stylesheet" />
  <link href="<?php echo e(url('')); ?>/assets/fontawesome/css/brands.css" rel="stylesheet" />
  <link href="<?php echo e(url('')); ?>/assets/fontawesome/css/solid.css" rel="stylesheet" />
  <?php echo $__env->yieldPushContent('toast-styles'); ?>
  <?php echo $__env->yieldPushContent('styles'); ?>
  <?php echo app('Illuminate\Foundation\Vite')('resources/css/app.scss'); ?>
 </head>
 <body class="no-scroll antialiased flex h-full text-base text-gray-700 dark:bg-coal-500">
  <!-- Theme Mode -->
  <script>
   const defaultThemeMode = 'light'; // light|dark|system
		let themeMode;

		if ( document.documentElement ) {
			if ( localStorage.getItem('theme')) {
					themeMode = localStorage.getItem('theme');
			} else if ( document.documentElement.hasAttribute('data-theme-mode')) {
				themeMode = document.documentElement.getAttribute('data-theme-mode');
			} else {
				themeMode = defaultThemeMode;
			}

			if (themeMode === 'system') {
				themeMode = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
			}

			document.documentElement.classList.add(themeMode);
		}
  </script>
  <!-- End of Theme Mode -->
  <?php echo $__env->yieldContent('toast'); ?>

  <?php echo $__env->yieldContent('content'); ?>

  <!-- Scripts -->
  <script src="<?php echo e(url('')); ?>/assets/js/core.bundle.js"></script>
  <script src="<?php echo e(url('')); ?>/assets/js/jquery/jquery3.7.1.min.js"></script>
  <script src="<?php echo e(url('')); ?>/assets/vendors/apexcharts/apexcharts.min.js"></script>
  <script src="<?php echo e(url('')); ?>/assets/js/myToast.js"></script>
  <?php echo $__env->yieldPushContent('toast-scripts'); ?>
  <?php echo $__env->yieldPushContent('scripts'); ?>
  <!-- End of Scripts -->
  <?php echo app('Illuminate\Foundation\Vite')('resources/js/app.js'); ?> 
 </body>
</html>
<?php /**PATH C:\laragon\www\grafindo-media-library\resources\views/layouts/guest.blade.php ENDPATH**/ ?>