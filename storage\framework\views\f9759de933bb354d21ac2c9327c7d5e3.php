
<div class="table-view-container">
  <table class="table-view">
    <thead>
      <tr>
        <th class="name-column">Name</th>
        <th class="date-column">Date Uploaded</th>
        <th class="type-column">Type</th>
        <th class="creator-column">Creator</th>
      </tr>
    </thead>
    <tbody>
      <?php $__currentLoopData = $data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
      <tr class="table-row cursor-pointer" data-id="<?php echo e($item->id); ?>" data-modal-toggle="#detail-modal-<?php echo e($item->id); ?>">
        <td class="name-column">
          <div class="file-info">
            <div class="file-icon">
              <?php
                $iconClass = '';
                $iconColor = '';

                if($item->media_type == "Document") {
                  if(str_ends_with(strtolower($item->filename), '.pdf')) {
                    $iconClass = 'fa-solid fa-file-pdf';
                    $iconColor = '#e53e3e'; // Merah untuk PDF
                  } elseif(str_ends_with(strtolower($item->filename), '.doc') || str_ends_with(strtolower($item->filename), '.docx')) {
                    $iconClass = 'fa-solid fa-file-word';
                    $iconColor = '#3182ce'; // Biru untuk Word
                  } elseif(str_ends_with(strtolower($item->filename), '.xls') || str_ends_with(strtolower($item->filename), '.xlsx')) {
                    $iconClass = 'fa-solid fa-file-excel';
                    $iconColor = '#38a169'; // Hijau untuk Excel
                  } elseif(str_ends_with(strtolower($item->filename), '.ppt') || str_ends_with(strtolower($item->filename), '.pptx')) {
                    $iconClass = 'fa-solid fa-file-powerpoint';
                    $iconColor = '#dd6b20'; // Oranye untuk PowerPoint
                  } elseif(str_ends_with(strtolower($item->filename), '.txt')) {
                    $iconClass = 'fa-solid fa-file-lines';
                    $iconColor = '#718096'; // Abu-abu untuk TXT
                  } else {
                    $iconClass = 'fa-solid fa-file';
                    $iconColor = '#718096'; // Abu-abu untuk file lainnya
                  }
                } elseif($item->media_type == "Audio") {
                  $iconClass = 'fa-solid fa-file-audio';
                  $iconColor = '#805ad5'; // Ungu untuk Audio
                } elseif($item->media_type == "Video") {
                  $iconClass = 'fa-solid fa-file-video';
                  $iconColor = '#dd6b20'; // Oranye untuk Video
                } else {
                  $iconClass = 'fa-solid fa-file-image';
                  $iconColor = '#38a169'; // Hijau untuk Image
                }
              ?>
              <i class="<?php echo e($iconClass); ?>" style="color: <?php echo e($iconColor); ?> !important;"></i>
            </div>
            <span class="file-name"><?php echo e($item->filename); ?></span>
          </div>
        </td>
        <td class="date-column"><?php echo e(\Carbon\Carbon::parse($item->upload_date)->setTimezone('Asia/Jakarta')->format('d/m/Y H:i')); ?></td>
        <td class="type-column"><?php echo e($item->media_type); ?> File</td>
        <td class="creator-column"><?php echo e($item->creator_name ?? 'Unknown'); ?></td>
      </tr>
      <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </tbody>
  </table>
</div>
<?php /**PATH C:\laragon\www\grafindo-media-library\resources\views/pages/explore/table-view.blade.php ENDPATH**/ ?>