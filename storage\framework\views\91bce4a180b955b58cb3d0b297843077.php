
<div id="banner-container" class="z-15 hidden items-center fixed top-[135px] right-0 h-16 bg-neutral-600 shadow-md">
  <div class="flex items-center justify-between h-full w-full px-4 md:px-6">
    <div class="flex items-center gap-2 md:gap-4">
      <button class="btn btn-icon btn-sm bg-neutral-700 hover:bg-neutral-800" id="btn-close-banner">
        <i class="ki-filled ki-cross text-white"></i>
      </button>
      <span class="text-white font-medium text-sm md:text-base" id="banner-title"></span>
    </div>
    <?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'bulk delete.uploads')): ?>
    <button class="btn btn-sm bg-red-500 hover:bg-red-600 p-2 flex flex-row items-center gap-2" id="btn-bulk-delete">
      <i class="ki-filled ki-trash text-white"></i>
      <span class="text-white text-sm md:text-base">Hapus</span>
    </button>
    <?php endif; ?>
  </div>
</div>


<div class="mb-2">
  <!-- Container -->
  <div id="page-title" class="container-fixed items-center justify-between flex-wrap gap-5 lg:gap-0">
    <div class="flex flex-col mb-2 justify-center items-start flex-wrap gap-1 lg:gap-2">
      <h1 class="font-medium text-lg text-gray-900">
        <span id="status-title"><?php echo e(@$page); ?></span>
        <span class="badge badge-outline badge-dark ml-2" id="status-badge"></span>
      </h1>
    </div>
  </div>
  <!-- End of Container -->
</div>

<!-- Container -->
<div class="container-fixed max-h-fit">
  <div class="flex items-center flex-wrap md:flex-nowrap lg:items-end justify-between border-b border-b-gray-200 dark:border-b-coal-100 gap-3 lg:gap-6 mb-5 lg:mb-6">
    <!-- Hidden tabs for JavaScript functionality -->
    <div class="hidden">
      <div class="tabs gap-5" data-tabs="false" id="my_tabs">
      <?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'uploads.not_submitted')): ?>
      <button class="tab active" data-tab-toggle="#tab_draft" data-status="Draft"></button>
      <?php endif; ?>
      <?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'uploads.pending')): ?>
      <button class="tab" data-tab-toggle="#tab_pending" data-status="Pending"></button>
      <?php endif; ?>
      <?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'uploads.rejected')): ?>
      <button class="tab" data-tab-toggle="#tab_rejected" data-status="Rejected"></button>
      <?php endif; ?>
      <?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'uploads.approved')): ?>
      <button class="tab" data-tab-toggle="#tab_approved" data-status="Approved"></button>
      <?php endif; ?>
      <?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'uploads.published')): ?>
      <button class="tab" data-tab-toggle="#tab_published" data-status="Published"></button>
      <?php endif; ?>
      </div>
    </div>
    <div class="flex items-center justify-end grow lg:grow-0 lg:pb-4 gap-2.5 mb-3 lg:mb-0">
    <?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'uploads.upload_now')): ?>
    <button class="btn btn-primary" data-modal-toggle="#myDropzone" id="upload-now-button">
      <i class="ki-filled ki-folder-up">
      </i>
      Upload Now
    </button>
    <?php endif; ?>
    </div>
  </div>
</div>
<?php /**PATH C:\laragon\www\grafindo-media-library\resources\views/pages/submit-content/components/header.blade.php ENDPATH**/ ?>