<?php $__env->startSection('title', 'Explore'); ?>

<?php $__env->startPush('styles'); ?>
<link href="<?php echo e(url('')); ?>/assets/css/explore-gallery.css" rel="stylesheet">
<link href="<?php echo e(url('')); ?>/assets/css/explore-details-view.css" rel="stylesheet">
<link href="<?php echo e(url('')); ?>/assets/css/explore-table-view.css" rel="stylesheet">
<link href="<?php echo e(url('')); ?>/assets/css/pdf-viewer.css" rel="stylesheet">
<style>
  /* All checkbox styling has been removed as we're now using the same HTML structure as the submit-content section */

  /* Ensure buttons and checkboxes are above the overlay */
  .gallery-item .btn-delete,
  .gallery-item .btn-collection,
  .gallery-item .btn-download,
  .gallery-item .download-btn,
  .gallery-item .checkbox-container {
    position: relative;
    z-index: 30;
  }

  /* Download button styling */
  .download-btn {
    min-width: 40px;
    height: 40px;
    transition: all 0.3s ease;
  }

  /* Responsive styles for download button */
  @media (max-width: 640px) {
    .download-btn {
      width: 40px;
      height: 40px;
      padding: 8px;
    }
    .download-text {
      display: none;
    }
  }

  @media (min-width: 641px) {
    .download-btn {
      min-width: 40px;
      padding: 8px 16px;
    }
  }

  /* Prevent the modal toggle from capturing clicks on buttons */
  .gallery-item [data-modal-toggle] {
    pointer-events: none;
  }

  /* But allow clicks on the image itself */
  .gallery-item img {
    pointer-events: auto;
  }

  /* Hide PDF viewer toolbar */
  .pdf-viewer-no-toolbar {
    border: none;
  }

  /* View mode dropdown styling */
  #view-mode-menu {
    box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1);
    border-radius: 0.475rem;
    padding: 0.625rem 0;
  }

  #view-mode-menu .dropdown-item {
    transition: background-color 0.2s ease;
  }

  #view-mode-menu .dropdown-item:hover {
    background-color: #f8f9fa;
  }

  #view-mode-menu .dropdown-item.active {
    background-color: #f1f3f5;
  }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fixed mb-8">
  <div class="flex flex-col items-center justify-center gap-2.5 flex-wrap">
    <h1 class="text-5xl">Smart creativity, Smart Work</h1>
    <h4 class="text-lg">Everything you need, from stock images and videos to a complete set of creative resources.</h4>

    <div class="flex items-center justify-center w-full">
      <form class="lg:w-[45%] my-5">
        <div class="input-group w-full">
          <select class="select lg:w-[35%] hidden lg:flex" name="media_type">
            <option value="">
            All Media
            </option>
            <?php if(isset($explorePermissions['canView']['photo']) && $explorePermissions['canView']['photo']): ?>
            <option value="Photo" <?php echo e(request('media_type') == "Photo" ? 'selected' : ''); ?>>
            Photo
            </option>
            <?php endif; ?>
            <?php if(isset($explorePermissions['canView']['illustration']) && $explorePermissions['canView']['illustration']): ?>
            <option value="Illustration" <?php echo e(request('media_type') == "Illustration" ? 'selected' : ''); ?>>
            Illustration
            </option>
            <?php endif; ?>
            <?php if(isset($explorePermissions['canView']['video']) && $explorePermissions['canView']['video']): ?>
            <option value="Video" <?php echo e(request('media_type') == "Video" ? 'selected' : ''); ?>>
            Video
            </option>
            <?php endif; ?>
            <?php if(isset($explorePermissions['canView']['audio']) && $explorePermissions['canView']['audio']): ?>
            <option value="Audio" <?php echo e(request('media_type') == "Audio" ? 'selected' : ''); ?>>
            Audio
            </option>
            <?php endif; ?>
            <?php if(isset($explorePermissions['canView']['document']) && $explorePermissions['canView']['document']): ?>
            <option value="Document" <?php echo e(request('media_type') == "Document" ? 'selected' : ''); ?>>
            Document
            </option>
            <?php endif; ?>
          </select>
          <input class="input ml-2" placeholder="Search anything here..." type="text" name="search" value="<?php echo e(request('search')); ?>"/>
          <button type="submit" class="btn btn-primary">
            <i class="ki-filled ki-magnifier"></i>
            <span class="hidden lg:flex">Search</span>
          </button>
        </div>
      </form>
      <button class="btn btn-outline btn-primary ml-2" data-modal-toggle="#search-modal">
        <i class="ki-filled ki-setting-4"></i>
      </button>
      <div class="dropdown ml-2" id="view-mode-dropdown" data-dropdown="true" data-dropdown-trigger="click">
        <button class="btn btn-outline btn-primary dropdown-toggle" type="button">
          <i class="ki-filled ki-eye"></i>
        </button>
        <div class="dropdown-content min-w-[150px]" id="view-mode-menu">
          <div class="dropdown-item cursor-pointer view-mode-option py-2 px-3" data-mode="large-icon">
            <div class="flex items-center gap-3">
              <i class="ki-filled ki-grid-2 text-gray-600"></i>
              <span class="font-medium">Large Icon</span>
            </div>
          </div>
          <div class="dropdown-item cursor-pointer view-mode-option py-2 px-3" data-mode="table">
            <div class="flex items-center gap-3">
              <i class="ki-filled ki-row-horizontal text-gray-600"></i>
              <span class="font-medium">Details</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <?php echo $__env->make('pages.explore.advanced-search', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
  </div>
</div>

<!-- Container -->
<?php if($data->isEmpty()): ?>
  <?php echo $__env->make('components.empty-content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php endif; ?>

<div class="container-fixed">
  <div class="flex justify-between items-center my-5">
    <div>
      <?php echo e($data->links('vendor.pagination.tailwind')); ?>

    </div>

    <!-- Bulk Delete Button - Initially Hidden -->
    <div id="bulk-actions" class="hidden">
      <button id="btn-bulk-delete" class="btn btn-danger">
        <i class="ki-outline ki-trash"></i>
        <span>Delete Selected</span>
      </button>
    </div>
  </div>

  <div id="content-container" class="large-icon-view">
    <?php echo $__env->make('pages.explore.content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('pages.explore.details-view', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('pages.explore.table-view', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
  </div>

  <div class="my-5">
    <?php echo e($data->links('vendor.pagination.tailwind')); ?>

  </div>

  

  <?php echo $__env->make('pages.explore.collection-drawer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="<?php echo e(url('')); ?>/assets/js/explore-grid.js"></script>
<script src="<?php echo e(url('')); ?>/assets/js/pdf-viewer.js"></script>
<script src="<?php echo e(url('')); ?>/assets/js/doc-preview.js"></script>
<script>
  // Pass permissions to JavaScript
  window.explorePermissions = <?php echo json_encode($explorePermissions ?? [], 15, 512) ?>;

  // Global delete content function - defined outside document.ready to be available immediately
  function deleteContent(e, contentId, mediaType) {
    e.preventDefault();
    e.stopPropagation();

    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      customClass:{
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-light',
      },
      confirmButtonText: "Yes, delete it!"
    }).then((result) => {
      if (result.isConfirmed) {
        $.ajax({
          url: "/explore/delete/" + contentId,
          method: "DELETE",
          data: {
            '_token': "<?php echo e(csrf_token()); ?>"
          },
          success: function(res) {
            myToast(res.status, res.message);
            // Remove the item from the view
            $(`[data-id="${contentId}"]`).fadeOut(300, function() {
              $(this).remove();
              // Reinitialize grid if in large icon mode
              if ($('#content-container').hasClass('large-icon-view')) {
                new ExploreGrid('#content-container', '.gallery-item', {
                  rowTargetHeight: 250,
                  minImagesPerRow: 5,
                  maxImagesPerRow: 5,
                  itemGap: 3
                });
              }
            });
          },
          error: function(err) {
            let res = err.responseJSON;
            myToast('error', res.message || 'An error occurred while deleting the item');
          }
        });
      }
    });
  }
</script>
<script>
  $(document).ready(function(){
    // View mode handling
    let currentViewMode = localStorage.getItem('exploreViewMode') || 'large-icon';

    // Initialize view mode
    setViewMode(currentViewMode);

    // View mode toggle
    $('.view-mode-option').on('click', function() {
      const mode = $(this).data('mode');
      setViewMode(mode);
      localStorage.setItem('exploreViewMode', mode);
      // Hide dropdown after selection
      const dropdown = KTDropdown.getInstance(document.getElementById('view-mode-dropdown'));
      if (dropdown) {
        dropdown.hide();
      }
    });

    function setViewMode(mode) {
      const container = $('#content-container');

      // Update dropdown button icon
      if (mode === 'large-icon') {
        $('#view-mode-dropdown .dropdown-toggle i').removeClass().addClass('ki-filled ki-grid-2');
      } else {
        $('#view-mode-dropdown .dropdown-toggle i').removeClass().addClass('ki-filled ki-row-horizontal');
      }

      // Update active class in dropdown menu
      $('.view-mode-option').removeClass('active');
      $(`.view-mode-option[data-mode="${mode}"]`).addClass('active');

      // Update container class
      container.removeClass('large-icon-view details-view table-view');
      container.addClass(mode + '-view');

      // Reinitialize grid if in large icon mode
      if (mode === 'large-icon') {
        new ExploreGrid('#content-container', '.gallery-item', {
          rowTargetHeight: 250,
          minImagesPerRow: 5,
          maxImagesPerRow: 5,
          itemGap: 3
        });
      }
    }
    const drawerEl = document.querySelector('#collection_drawer');
    const drawer = KTDrawer.getInstance(drawerEl);
    const formSearch = $("#form-advanced-search");
    let next_page = "<?php echo e($data->nextPageUrl()); ?>";
    let content_id;

    // let $paginationContainer = $("#pagination-container");
    // $paginationContainer.empty();
    // console.log(data)
    // if (data.links) {
    //   let active = `class="z-10 page-link flex items-center justify-center px-3 h-8 leading-tight text-blue-600 border border-blue-300 bg-blue-50 hover:bg-blue-100 hover:text-blue-700 dark:border-gray-700 dark:bg-gray-700 dark:text-white"`;
    //   let inactive = `class="page-link flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white"`;

    //   data.links.forEach(link => {
    //     let navigation = link.label;
    //     let pageLink;

    //     if(navigation.toLowerCase().includes("previous")){
    //       pageLink = `
    //         <li>
    //           <a href="#" data-url="${link.url}" class="page-link flex items-center justify-center px-3 h-8 ms-0 leading-tight text-gray-500 bg-white border border-e-0 border-gray-300 rounded-s-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
    //             <span class="sr-only">${link.label}</span>
    //             <svg class="w-2.5 h-2.5 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
    //               <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 1 1 5l4 4"/>
    //             </svg>
    //           </a>
    //         </li>
    //       `;
    //     }else if(navigation.toLowerCase().includes("next")){
    //       pageLink = `
    //         <li>
    //           <a href="#" data-url="${link.url}" class="page-link flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 rounded-e-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
    //             <span class="sr-only">${link.label}</span>
    //             <svg class="w-2.5 h-2.5 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
    //               <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
    //             </svg>
    //           </a>
    //         </li>
    //       `;
    //     }else{
    //       pageLink = `
    //         <li>
    //           <a href="#" data-url="${link.url}" ${link.active ? active+' aria-current="page"' : inactive}>${link.label}</a>
    //         </li>
    //       `;
    //     }

    //     $paginationContainer.append(pageLink);
    //   });

    //   $(".page-link").on("click", function (e) {
    //       e.preventDefault();
    //       let url = $(this).data("url");
    //       detailContent.html(emptyContent);
    //       getContent(status, filter, authorId, url);
    //   });
    // }

    // if(next_page == "") $(".show-more").hide();
    // else $(".show-more").show();

    // $(".show-more").on("click", function(e){
    //   if(next_page){
    //     loadMoreContent();
    //   }
    // })

    // function loadMoreContent() {
    //   $(".loading").show();
    //   $(".show-more").hide();
    //   $.get(next_page, function(response) {
    //       if (response.data) {
    //         $("#content-container").append(response.data);
    //         next_page = response.next_page;
    //       } else {
    //         next_page = null;
    //       }
    //       if(next_page) $(".show-more").show();
    //       $(".loading").hide();
    //   });
    // }

    function getCollection() {
      $("#collection-list").empty();
      $.ajax({
        url: "<?php echo e(route('get-collections')); ?>",
        method: "GET",
        beforeSend: function() {
          $(".collection-loading").show()
        },
        success: function(res) {
          $(".collection-loading").hide()

          let data = res.data
          data.forEach(item=> {
            $("#collection-list").append(`
              <div class="flex justify-between gap-2">
                <button class="flex flex-row hover:bg-gray-200 rounded-md w-full h-10 p-2 btn-item-collection" data-id="${item.id}">
                  <i class="ki-filled ki-right flex items-center"></i>
                  <i class="hidden ki-filled ki-check text-success flex items-center"></i>
                  <span class="menu-title ml-2 flex items-center">${item.name}</span>
                </button>
                <a href="" class="flex items-center"><i class="ki-outline ki-exit-right-corner hover:text-blue-500"></i></a>
              </div>
            `)
          })
        },
        error: function(err) {
          let res = err.responseJSON
          myToast(res.status, res.message)
        }
      });
    }

    $(".btn-collection").on("click", function(e){
      e.stopPropagation();
      let mediaType = $(this).data("media-type");

      // Check if user has collection permission for this media type
      if (window.explorePermissions &&
          window.explorePermissions.canAddToCollection &&
          window.explorePermissions.canAddToCollection[mediaType]) {

        drawer.toggle();
        getCollection();
        $("#btn-save-collection").hide();
        $(".collection-footer").hide();
        $("#btn-create-collection").show();
        let card = $(this).closest(".group");
        content_id = card.data("id");
      } else {
        myToast('error', 'You do not have permission to add this item to collections');
      }
    })

    $(document).on("click", ".btn-item-collection", function(e){
      let isSelected = $(this).hasClass("selected");

      $(".btn-item-collection").removeClass("selected bg-gray-200");
      $(".btn-item-collection .ki-right").show();
      $(".btn-item-collection .ki-check").addClass("hidden").removeClass("flex");

      if (isSelected) {
          $("#btn-save-collection").hide();
          $("#btn-create-collection").show();
      } else {
          $(this).addClass("selected bg-gray-200");
          $(this).find(".ki-right").hide();
          $(this).find(".ki-check").addClass("flex").removeClass("hidden");

          $("#btn-save-collection").show();
          $("#btn-create-collection").hide();
      }
    })

    $(".btn-batal-koleksi").on("click", function() {
      let selectedItem = $(".btn-item-collection.selected");

      if (selectedItem.length) {
          let iconRight = selectedItem.find(".ki-right");
          let iconCheck = selectedItem.find(".ki-check");

          selectedItem.removeClass("selected bg-gray-200");
          iconRight.show();
          iconCheck.addClass("hidden").removeClass("flex");
      }

      $("#btn-save-collection").hide()
      $('#btn-create-collection').show()
    })

    $(".btn-simpan-koleksi").on("click", function() {
      let isSelected = $(".btn-item-collection.selected");
      let collectionId = isSelected.data("id")

      $.ajax({
        url: "<?php echo e(route('add-item-collection')); ?>",
        method: "POST",
        data: {
          '_token': "<?php echo e(csrf_token()); ?>",
          'collection_id': collectionId,
          'content_id': content_id,
        },
        beforeSend: function() {
          $('.collection-footer').hide()
          $("#btn-save-collection").hide()
          $(".collection-footer-loading").show()
        },
        success: function(res) {
          $(".collection-footer-loading").hide()
          $("#btn-save-collection").hide()
          $("#btn-create-collection").show()
          getCollection()
          myToast(res.status, res.message)
        },
        error: function(err) {
          let res = err.responseJSON
          let status = 'error'
          console.log(res)
          myToast(status, res.message)
          $(".collection-footer-loading").hide()
        }
      });
    })

    $("#btn-create-collection").on("click", function() {
      $('.collection-footer').show()
      $("#collection_input").val("")
      $(this).hide();
    })

    $("#btn-cancel-create-collection").on("click", function() {
      $('.collection-footer').hide()
      $('#btn-create-collection').show()
    })

    $("#collection_input").on("keyup", function() {
      let input = $(this).val().trim()
      if(input === ""){
        $("#btn-store-collection").prop('disabled', true)
      }else{
        $("#btn-store-collection").prop('disabled', false)
      }
    })

    $("#btn-store-collection").on("click", function() {
      let inputName = $("#collection_input").val().trim()

      $.ajax({
        url: "<?php echo e(route('store-collection')); ?>",
        method: "POST",
        data: {
          '_token': '<?php echo e(csrf_token()); ?>',
          'name': inputName
        },
        beforeSend: function() {
          $('.collection-footer').hide()
          $(".collection-footer-loading").show()
        },
        success: function(res) {
          console.log(res)
          $(".collection-footer-loading").hide()
          $('#btn-create-collection').show()
          getCollection()
          myToast(res.status, res.message)
        },
        error: function(err) {
          let res = err.responseJSON
          console.log(res)
          myToast(res.status, res.message)
        }
      });
    })

    function updateFavorite(contentId, type) {
      $.ajax({
        url: 'explore/favorite',
        method: 'PUT',
        data: {
          '_token': "<?php echo e(csrf_token()); ?>",
          'id': contentId,
          'type': type
        },
        success: function (res){
          myToast(res.status, res.message)
        },
        error: function (err){
          let res = err.responseJSON;
          myToast(res.status, res.message)
        }
      });
    }

    $(".btn-favorite").on("click", function(e){
      e.stopPropagation();
      let card = $(this).closest(".group");
      let cardId = card.data("id");
      let buttonFav = $(this);
      let buttonLike = $(".btn-like");
      let type = buttonFav.data("type");

      buttonFav.addClass("scale-125").delay(200).queue(function(next) {
          $(this).removeClass("scale-125");
          next();
      });

      if (type === "like") {
          buttonFav.removeClass("bg-transparent border-white hover:bg-slate-50 hover:bg-opacity-10")
                .addClass("bg-red-500 hover:bg-red-600")
                .data("type", "dislike")
                .find(".tooltip").text("Unfavorite");

          buttonLike.removeClass("btn-light")
                .addClass("btn-danger")
                .data("type", "dislike")
                .find(".text-btn").text("Hapus dari favorit");
      } else {
          buttonFav.removeClass("bg-red-500 hover:bg-red-600")
                .addClass("bg-transparent border-white hover:bg-slate-50 hover:bg-opacity-10")
                .data("type", "like")
                .find(".tooltip").text("Favorite");

          buttonLike.removeClass("btn-danger")
                .addClass("btn-light")
                .data("type", "like")
                .find(".text-btn").text("Tambahkan ke favorit");
      }

      updateFavorite(cardId, type);
    })

    $(".btn-like").on("click", function(e){
      let card = $(this).closest(".group");
      let cardId = card.data("id");
      let buttonLike = $(this);
      let buttonFav = $(".btn-favorite");
      let type = buttonLike.data("type");

      buttonLike.addClass("scale-120").delay(200).queue(function(next) {
          $(this).removeClass("scale-120");
          next();
      });

      if (type === "like") {
          buttonLike.removeClass("btn-light")
                .addClass("btn-danger")
                .data("type", "dislike")
                .find(".text-btn").text("Hapus dari favorit");

          buttonFav.removeClass("bg-transparent border-white hover:bg-slate-50 hover:bg-opacity-10")
                .addClass("bg-red-500 hover:bg-red-600")
                .data("type", "dislike")
                .find(".tooltip").text("Unfavorite");
      } else {
          buttonLike.removeClass("btn-danger")
                .addClass("btn-light")
                .data("type", "like")
                .find(".text-btn").text("Tambahkan ke favorit");

          buttonFav.removeClass("bg-red-500 hover:bg-red-600")
                .addClass("bg-transparent border-white hover:bg-slate-50 hover:bg-opacity-10")
                .data("type", "like")
                .find(".tooltip").text("Favorite");
      }

      updateFavorite(cardId, type);
    })

    // Handle both old and new download buttons
    $(".btn-download, .download-btn").on("click", function(e){
      e.stopPropagation();
      let card = $(this).closest(".group");
      let cardId = card.data("id");
      let mediaType = $(this).data("media-type");

      // Check if user has download permission for this media type
      if (window.explorePermissions &&
          window.explorePermissions.canDownload &&
          window.explorePermissions.canDownload[mediaType]) {

        setTimeout(() => {
          $.ajax({
            url: "explore/downloads-count/"+cardId,
            method: 'GET',
            success: function(res){
              $("#detail-modal-"+cardId).find("#downloads_count").text(` ${res.data.downloads_count}`)
              myToast(res.status, res.message)
            }
          });
        }, 2000);
      } else {
        myToast('error', 'You do not have permission to download this file');
        e.preventDefault();
        return false;
      }
    })

    $("#btn-reset-advanced-search").on("click", function(){
      $("#media_type").val("").change()
      $("#category").val("").change()
      $("#category_2").val("").change()
      $("#keywords").val("").change()
      $("#orientation").val("").change()
      $(".checkbox").prop("checked", false)
    })

    $("#btn-advanced-search").on("click", function(){
      formSearch.submit();
    })

    $('#keywords').select2({
      tokenSeparators: [',', ' '],
      placeholder: "Pilih Kata Kunci",
    });

    // We're now using the global deleteContent function with inline onclick handlers
    // This ensures the confirmation dialog appears properly

    // Bulk delete functionality
    let selectedItems = [];

    // Handle checkbox selection
    $(document).on("change", ".bulk-select-checkbox", function(e) {
      e.preventDefault();
      e.stopPropagation();
      const contentId = $(this).data("id");
      const $checkbox = $(this);
      const $container = $checkbox.closest('.multiple-select');

      if ($(this).is(":checked")) {
        selectedItems.push(contentId);
        $container.find('.unchecked').hide();
        $container.find('.checked').removeClass('hidden');
      } else {
        selectedItems = selectedItems.filter(id => id !== contentId);
        $container.find('.unchecked').show();
        $container.find('.checked').addClass('hidden');
      }

      // Show/hide bulk actions
      if (selectedItems.length > 0) {
        $("#bulk-actions").removeClass("hidden");
      } else {
        $("#bulk-actions").addClass("hidden");
      }
    });

    // Bulk delete button
    $("#btn-bulk-delete").on("click", function() {
      if (selectedItems.length === 0) {
        myToast('error', 'No items selected');
        return;
      }

      Swal.fire({
        title: "Delete selected items?",
        text: `You are about to delete ${selectedItems.length} item(s). This cannot be undone!`,
        icon: "warning",
        showCancelButton: true,
        customClass:{
          confirmButton: 'btn btn-danger',
          cancelButton: 'btn btn-light',
        },
        confirmButtonText: "Yes, delete them!"
      }).then((result) => {
        if (result.isConfirmed) {
          $.ajax({
            url: "/explore/bulk-delete",
            method: "DELETE",
            data: {
              '_token': "<?php echo e(csrf_token()); ?>",
              'ids': selectedItems
            },
            success: function(res) {
              myToast(res.status, res.message);

              // Remove the items from the view
              selectedItems.forEach(id => {
                $(`[data-id="${id}"]`).fadeOut(300, function() {
                  $(this).remove();
                });
              });

              // Reset selection
              selectedItems = [];
              $("#bulk-actions").addClass("hidden");

              // Reinitialize grid if in large icon mode
              if ($('#content-container').hasClass('large-icon-view')) {
                new ExploreGrid('#content-container', '.gallery-item', {
                  rowTargetHeight: 250,
                  minImagesPerRow: 5,
                  maxImagesPerRow: 5,
                  itemGap: 3
                });
              }
            },
            error: function(err) {
              let res = err.responseJSON;
              myToast('error', res.message || 'An error occurred while deleting the items');
            }
          });
        }
      });
    });

  })
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.partials.main', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\grafindo-media-library\resources\views/pages/explore/index.blade.php ENDPATH**/ ?>