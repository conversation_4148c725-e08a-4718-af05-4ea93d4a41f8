<script>
  // This script uses global variables defined in main-scripts.blade.php

  // Update the hidden input with the current keywords
  function updateHiddenInput() {
      $("#hiddenKeywords").val(selectedKeywords.join(','));
    }

  // Update the UI based on the current keyword count
  function updateKeywordUI() {
      // Update the counter
      $("#keywords_counter").text(selectedKeywords.length);

      // Show/hide warning based on minimum requirement (5 keywords)
      if (selectedKeywords.length < 5) {
        $("#key_warning").show();
      } else {
        $("#key_warning").hide();
      }

      // Enable/disable input based on maximum limit (20 keywords)
      if (selectedKeywords.length >= 20) {
        $("#keywordInput").prop("disabled", true);
      } else {
        $("#keywordInput").prop("disabled", false);
      }
    }

  // Add a single keyword to the list
  function addKeyword(keyword) {
      // Skip if empty or already exists
      keyword = keyword.trim();
      if (keyword === '' || selectedKeywords.includes(keyword)) {
        return;
      }

      // Add to array
      selectedKeywords.push(keyword);

      // Create visual tag
      let container = (status === "Draft") ? $("#keywordsContainerDraft") : $("#keywordsContainerShow");
      let tag;

      if (status === "Draft") {
        tag = `
          <span class="keyword-item px-3 py-2 bg-neutral-200 text-xs rounded-full flex items-center">
              ${keyword}
              <button type="button" class="ml-2 text-white remove-keyword" data-keyword="${keyword}">
                <i class="ki-filled ki-cross text-gray-700"></i>
              </button>
          </span>
        `;
      } else {
        tag = `
          <span class="keyword-item px-3 py-2 bg-neutral-200 text-xs rounded-full flex items-center">
              ${keyword}
          </span>
        `;
      }

      container.append(tag);

      // Update UI and hidden input
      updateHiddenInput();
      updateKeywordUI();
    }

  // Process multiple keywords (comma-separated)
  function processKeywords(input) {
      if (!input || input.trim() === '') {
        return;
      }

      // Split by comma, trim each part, and filter out empty strings
      let keywords = input.split(',')
        .map(k => k.trim())
        .filter(k => k.length > 0);

      // Add each keyword
      keywords.forEach(keyword => addKeyword(keyword));
    }

  // Load keywords for an existing content
  function loadKeywords(contentId) {
      $.ajax({
        url: `submit-content/get-keywords/${contentId}`,
        method: "GET",
        success: function (response) {
          // Clear existing keywords
          selectedKeywords = [];
          let container = (status === "Draft") ? $("#keywordsContainerDraft") : $("#keywordsContainerShow");
          container.empty();

          // Add each keyword from the response
          response.data.forEach(keyword => {
            addKeyword(keyword);
          });
        }
      });
    }

    // EVENT HANDLERS

    // Remove keyword when clicking the X button
    $(document).on("click", ".remove-keyword", function () {
      let keyword = $(this).data("keyword");

      // Remove from array
      selectedKeywords = selectedKeywords.filter(k => k !== keyword);

      // Remove visual tag
      $(this).parent().remove();

      // Update UI and hidden input
      updateHiddenInput();
      updateKeywordUI();
    });

    // Update counter as user types
    $(document).on("keyup", "#keywordInput", function () {
      let inputValue = $(this).val().trim();
      let pendingCount = 0;

      if (inputValue.length > 0) {
        // Count comma-separated keywords in the input field
        pendingCount = inputValue.split(",")
          .map(k => k.trim())
          .filter(k => k.length > 0).length;
      }

      // Show total (existing + pending)
      $("#keywords_counter").text(selectedKeywords.length + pendingCount);
    });

    // Handle Enter key to add keywords
    $(document).on("keydown", "#keywordInput", function (e) {
      if (e.key === "Enter") {
        e.preventDefault();
        let input = $(this).val().trim();

        if (input.length > 0) {
          // Process the input as keywords
          processKeywords(input);

          // Clear the input field
          $(this).val("");
        }
      }
    });

    $(document).on("keyup", "#description-draft", function (e) {
      let descInput = $("#description-draft");
      let description = descInput.val().trim()
      descriptionCounter = description.split(" ").length
      if(descriptionCounter < 2){
        $("#description_warning").show()
        descInput.addClass("border-danger")
      }else{
        $("#description_warning").hide()
        descInput.removeClass("border-danger")
      }
    });

  function updateShow(){
      let cardId = selectedIds[0];
      let cardData = itemData[cardId];
      let statusKey = status.toLowerCase();
      let storagePath = "<?php echo e(asset('storage/')); ?>";

      detailContent.html(selectedIds.length == 0 ? emptyContent : showContent);

      if (selectedIds.length == 1) {
          $("#filename").text(cardData.filename);
          $("#description").text(cardData.description);
          $("#category").text(cardData.category.name);
          if(cardData.category_2 != null) $("#category_2").text(cardData.category_2.name);
          $("#media_type").text(cardData.media_type);
          $("#creator_name").text(cardData.creator_name);
          if(cardData.date_taken != null) $("#date_taken").text(cardData.date_taken.split(" ")[0]);
          if(cardData.release_document != null) $("#current_release_doc").html(`File saat ini: <a href="${storagePath}/${cardData.release_document}" target="_blank" class="text-blue-500 cursor-pointer">Lihat File</a>`);
          loadKeywords(cardId);

          $("#accordion_item").html(`
            <button class="accordion-toggle pb-2 group btn-review-history" data-id="${cardId}">
              <span class="">
              Riwayat Penilaian
              </span>
              <i class="ki-outline ki-down text-gray-600 text-2sm" id="plus-accordion-${cardId}">
              </i>
              <i class="ki-outline ki-up text-gray-600 text-2sm hidden" id="minus-accordion-${cardId}">
              </i>
            </button>
            <div class="text-center loading hidden mt-2">
              <div role="status">
                <svg aria-hidden="true" class="inline w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-gray-600 dark:fill-gray-300" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
                    <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/>
                </svg>
                <span class="sr-only">Loading...</span>
              </div>
            </div>
            <div class="accordion-content hidden" id="accordion-${cardId}"></div>
          `);
      }
      selectedKeywords = [];
      $("#keywordsContainerShow").empty();
    }

  function updateForm() {
      let cardId = selectedIds[0];
      let cardData = itemData[cardId];
      let statusKey = status.toLowerCase();
      let storagePath = "<?php echo e(asset('storage/')); ?>";

      detailContent.html(selectedIds.length == 0 ? emptyContent : formContent);
      inputContentId = $("#group-input-content-id");
      detailContentForm = $("#detail-content-form");

      if (selectedIds.length == 0) {
          btnUpdate.addClass("hidden").removeClass("flex");
          inputContentId.empty();
      } else {
          btnUpdate.removeClass("hidden").addClass("flex");
          inputContentId.empty();

          selectedIds.forEach(item => {
              inputContentId.append(`<input type="hidden" name="content_id[]" value="${item}">`);
          });

          $("#filename-"+statusKey).html("Banyak item terpilih");
          if (selectedIds.length == 1) {
              $("#filename-"+statusKey).html(cardData.filename);
              $("#description-"+statusKey).text(cardData.description);
              $("#category-"+statusKey).val(cardData.category_id).change();
              if(cardData.category_2_id != null) $("#category_2-"+statusKey).val(cardData.category_2_id).change();
              $("#media_type-"+statusKey).val(cardData.media_type).change();
              $("#creator_name-"+statusKey).val(cardData.creator_name);
              if(cardData.date_taken != null) $("#date_taken-"+statusKey).val(cardData.date_taken.split(" ")[0]);
              if(cardData.release_document != null) $("#current_release_doc-"+statusKey).html(`File saat ini: <a href="${storagePath}/${cardData.release_document}" target="_blank" class="text-blue-500 cursor-pointer">Lihat File</a>`);
              loadKeywords(cardId);
          }

          selectedKeywords = [];
          $("#keywordsContainerDraft").empty();
          updateHiddenInput();
      }
    }

    $('#btn-save').on('click', function(e){
      Swal.fire({
        title: "Are you sure?",
        text: "Your data will saved as draft.",
        icon: "warning",
        showCancelButton: true,
        customClass:{
          confirmButton: 'btn btn-light',
          cancelButton: 'btn btn-danger',
        },
        confirmButtonText: "Yes, save it!"
      }).then((result) => {
        if (result.isConfirmed) {
          $('#update-type').val('update')
          btnLoading.addClass("flex").removeClass("hidden");
          btnUpdate.removeClass("flex").addClass("hidden");
          detailContentForm.submit()
        }
      });
    });

    $('#btn-submit').on('click', function(){
      Swal.fire({
        title: "Are you sure?",
        text: "You won't be able to revert this!",
        icon: "warning",
        showCancelButton: true,
        customClass:{
          confirmButton: 'btn btn-light',
          cancelButton: 'btn btn-danger',
        },
        confirmButtonText: "Yes, submit it!"
      }).then((result) => {
        if (result.isConfirmed) {
          $('#update-type').val('submit')
          btnLoading.addClass("flex").removeClass("hidden");
          btnUpdate.removeClass("flex").addClass("hidden");
          detailContentForm.submit()
        }
      });
    });
</script>
<?php /**PATH C:\laragon\www\grafindo-media-library\resources\views/pages/submit-content/components/form-scripts.blade.php ENDPATH**/ ?>