<div class="modal group" data-modal="true" id="detail-modal-<?php echo e($item->id); ?>" data-id="<?php echo e($item->id); ?>">
  <div class="modal-content max-w-[600px] h-full <?php echo e($item->media_type == 'Document' ? 'lg:max-w-[85%] lg:w-[85%]' : 'lg:max-w-[1100px]'); ?> lg:h-[95%] modal-center-y scrollable-y">
   <div class="modal-body relative p-5 h-full">
    <button class="btn btn-xs btn-icon btn-light absolute top-2 right-2 lg:top-3 lg:right-0" data-modal-dismiss="true">
     <i class="ki-outline ki-cross">
     </i>
    </button>

    <div class="flex flex-col md:flex-row h-auto lg:h-[90%] gap-6 mt-5">
        <?php if($item->media_type == "Document"): ?>
        <!-- For Document Type: 3-column layout with preview in the middle -->

        <!-- Left Column: Thumbnail and Actions -->
        <div class="flex flex-col w-full lg:w-1/4">
          <div class="border flex items-center justify-center w-full h-[90%] overflow-hidden">
            <img src="<?php echo e(asset('assets/'.$item->thumbnail_path)); ?>" class="w-full h-auto">
          </div>
          <div class="flex justify-between mt-4 gap-2">
            <div class="flex gap-2">
              <?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'collection.explores.photo|collection.explores.illustration|collection.explores.video|collection.explores.audio|collection.explores.document')): ?>
              <button class="btn btn-sm btn-light btn-collection">
                <i class="ki-outline ki-add-folder"></i>
                <span>Tambahkan ke koleksi</span>
              </button>
              <?php endif; ?>

              <?php
                $mediaType = strtolower($item->media_type);
                $canDelete = isset($explorePermissions['canDelete'][$mediaType]) ? $explorePermissions['canDelete'][$mediaType] : false;
              ?>

              <?php if($canDelete): ?>
              <button type="button" class="btn btn-sm btn-danger btn-delete-detail" data-id="<?php echo e($item->id); ?>" data-media-type="<?php echo e($mediaType); ?>" onclick="event.stopPropagation(); deleteContent(event, '<?php echo e($item->id); ?>', '<?php echo e($mediaType); ?>')">
                <i class="ki-outline ki-trash"></i>
                <span>Delete</span>
              </button>
              <?php endif; ?>
            </div>
          </div>
        </div>

        <!-- Middle Column: Document Preview (Larger) -->
        <div class="hidden md:flex flex-col lg:w-1/2 h-full">
          <div class="flex-grow overflow-hidden">
            <?php
              $fileExtension = pathinfo($item->file_path, PATHINFO_EXTENSION);
              $isDocx = in_array(strtolower($fileExtension), ['doc', 'docx']);
            ?>

            <?php if($isDocx): ?>
              <div id="doc-preview-container-<?php echo e($item->id); ?>" class="w-full h-full flex items-center justify-center">
                <div class="doc-loading-indicator flex flex-col items-center">
                  <img src="<?php echo e(asset('assets/'.$item->thumbnail_path)); ?>" class="w-1/2 mb-4">
                  <div class="text-center">
                    <p class="mb-2">Converting document for preview...</p>
                    <div class="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]" role="status">
                      <span class="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">Loading...</span>
                    </div>
                  </div>
                </div>
                <div class="doc-preview-content hidden w-full h-full">
                  <object data="" type="application/pdf" class="w-full h-full pdf-viewer-no-toolbar">
                    <p>It appears your browser doesn't support PDFs. You can download the file instead.</p>
                  </object>
                </div>
              </div>
            <?php else: ?>
              <object data="<?php echo e(Storage::url($item->file_path)); ?>#toolbar=0&navpanes=0&scrollbar=0&view=FitH" type="application/pdf" class="w-full h-full pdf-viewer-no-toolbar">
                <p>It appears your browser doesn't support PDFs. You can <a href="<?php echo e(Storage::url($item->file_path)); ?>">download the PDF</a> instead.</p>
              </object>
            <?php endif; ?>
          </div>
        </div>

        <!-- Right Column: Description and Metadata -->
        <div class="md:w-1/4 mb-5">

        <?php else: ?>
        <!-- For Other Media Types: 2-column layout -->

        <!-- Left Column: Media Content and Actions -->
        <div class="flex flex-col w-full lg:w-2/3">
          <div class="border flex items-center justify-center w-full h-[90%] overflow-hidden">
            <?php if(in_array($item->media_type, ['Photo', 'Illustration'])): ?>
              <?php
              $ext = explode('.', $item->file_path);
              $path = $ext == "jpg" ? $item->file_path : $item->thumbnail_path;
              ?>
              <img src="<?php echo e(Storage::url($path)); ?>" class="max-w-auto <?php echo e($item->orientation == 'Landscape' ? 'h-auto':'h-full'); ?>">
            <?php elseif($item->media_type == "Video"): ?>
              <video controls controlsList="nodownload" class="h-full">
                <source src="<?php echo e(Storage::url($item->file_path)); ?>" type="video/mp4">
              </video>
            <?php elseif($item->media_type == "Audio"): ?>
              <div class="flex flex-col items-center justify-center w-full h-full">
                <img src="<?php echo e(asset('assets/thumbnail/thumbnail_audio.svg')); ?>" class="max-w-[200px] mb-8">
                <audio controls controlsList="nodownload" class="mb-4">
                  <source src="<?php echo e(Storage::url($item->file_path)); ?>" type="audio/mp3">
                </audio>
              </div>
            <?php endif; ?>
          </div>
          <div class="flex justify-between mt-4 gap-2">
            <div class="flex gap-2">
              <?php if (\Illuminate\Support\Facades\Blade::check('canAccess', 'collection.explores.photo|collection.explores.illustration|collection.explores.video|collection.explores.audio|collection.explores.document')): ?>
              <button class="btn btn-sm btn-light btn-collection">
                <i class="ki-outline ki-add-folder"></i>
                <span>Tambahkan ke koleksi</span>
              </button>
              <?php endif; ?>

              <?php
                $mediaType = strtolower($item->media_type);
                $canDelete = isset($explorePermissions['canDelete'][$mediaType]) ? $explorePermissions['canDelete'][$mediaType] : false;
              ?>

              <?php if($canDelete): ?>
              <button type="button" class="btn btn-sm btn-danger btn-delete-detail" data-id="<?php echo e($item->id); ?>" data-media-type="<?php echo e($mediaType); ?>" onclick="event.stopPropagation(); deleteContent(event, '<?php echo e($item->id); ?>', '<?php echo e($mediaType); ?>')">
                <i class="ki-outline ki-trash"></i>
                <span>Delete</span>
              </button>
              <?php endif; ?>
            </div>
          </div>
        </div>

        <!-- Right Column: Description and Metadata -->
        <div class="md:w-1/3 mb-5">
        <?php endif; ?>
            <h2 class="text-lg font-semibold">Description</h2>
            <p class="text-gray-700 text-sm mb-4"><?php echo e($item->description); ?></p>
            <h2 class="text-lg font-semibold">Metadata</h2>
            <ul class="list-disc list-inside text-gray-700 mb-4">
              <li class="text-sm mb-1"><strong>Nama File:</strong> <?php echo e($item->filename); ?></li>
              <li class="text-sm mb-1"><strong>Kategori 1:</strong> <?php echo e($item->category->name); ?></li>
              <li class="text-sm mb-1"><strong>Kategori 2:</strong> <?php echo e($item->category_2->name ?? '-'); ?></li>
              <?php
                $keywords = [];
                foreach ($item->keywords as $value) {
                  $keywords[] = $value->name;
                }
              ?>
              <li class="text-sm mb-1"><strong>Keywords:</strong> <?php echo e(implode(', ', $keywords)); ?></li>
              <li class="text-sm mb-1"><strong>Tipe Media:</strong> <?php echo e($item->media_type); ?></li>
              <li class="text-sm mb-1"><strong>Kreator:</strong> <?php echo e($item->creator_name); ?></li>
              <li class="text-sm mb-1"><strong>Pengunggah:</strong> <?php echo e($item->author->name); ?></li>
              <li class="text-sm mb-1"><strong>Tanggal Dibuat:</strong> <?php echo e(\Carbon\Carbon::parse($item->date_taken)->translatedFormat('d F Y')); ?></li>
              <li class="text-sm mb-1"><strong>Tanggal Diunggah:</strong> <?php echo e(\Carbon\Carbon::parse($item->upload_date)->translatedFormat('d F Y')); ?></li>
              <li class="text-sm mb-1"><strong>Jumlah Unduh:</strong><span id="downloads_count"> <?php echo e($item->downloads_count ?? '0'); ?></span> </li>
              <li class="text-sm mb-1"><strong>Dokumen Rilis:</strong><a href="<?php echo e(asset("storage/$item->release_document")); ?>" target="_blank" class="text-blue-500 cursor-pointer"> Lihat File</a> </li>
            </ul>


            
            <?php
              $mediaType = strtolower($item->media_type);
              $canDownload = isset($explorePermissions['canDownload'][$mediaType]) ? $explorePermissions['canDownload'][$mediaType] : false;
            ?>

            <?php if($canDownload): ?>
            <a href="/explore/download/<?php echo e($item->id); ?>" class="btn btn-dark btn-md w-full justify-center mt-2 btn-download" data-media-type="<?php echo e($mediaType); ?>">
              <i class="ki-filled ki-folder-down"></i>
              Download File
            </a>
            <?php endif; ?>
        </div>
    </div>

   </div>
  </div>
</div><?php /**PATH C:\laragon\www\grafindo-media-library\resources\views/pages/explore/detail.blade.php ENDPATH**/ ?>