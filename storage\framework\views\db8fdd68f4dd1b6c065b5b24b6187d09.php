<?php $__currentLoopData = $data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
<div class="relative group cursor-pointer gallery-item" data-id="<?php echo e($item->id); ?>" data-modal-toggle="#detail-modal-<?php echo e($item->id); ?>" data-orientation="<?php echo e($item->orientation); ?>" onclick="if(event.target === this || event.target.tagName === 'IMG' || event.target.classList.contains('transition-all')) { document.querySelector('#detail-modal-<?php echo e($item->id); ?>').classList.add('show'); }">
    <?php
        $mediaType = strtolower($item->media_type);
        $canAddToCollection = isset($explorePermissions['canAddToCollection'][$mediaType]) ? $explorePermissions['canAddToCollection'][$mediaType] : false;
        $canDelete = isset($explorePermissions['canDelete'][$mediaType]) ? $explorePermissions['canDelete'][$mediaType] : false;
        $canBulkDelete = isset($explorePermissions['canBulkDelete'][$mediaType]) ? $explorePermissions['canBulkDelete'][$mediaType] : false;
    ?>

    <!-- Bulk Delete Checkbox -->
    <?php if($canBulkDelete): ?>
    <div class="multiple-select bg-neutral-600 hover:bg-neutral-800 flex absolute top-1 left-1 justify-center items-center rounded-full w-7 h-7 cursor-pointer z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-200" onclick="event.stopPropagation();">
        <div class="w-4 h-4 rounded-sm bg-transparent border-white border-2 unchecked"></div>
        <i class="hidden ki-filled ki-check text-lg text-white checked"></i>
        <input type="checkbox" class="bulk-select-checkbox absolute opacity-0 w-full h-full cursor-pointer" data-id="<?php echo e($item->id); ?>" data-media-type="<?php echo e($mediaType); ?>">
    </div>
    <?php endif; ?>

    <div class="z-1 flex flex-col gap-2 absolute top-3 right-3 opacity-0 group-hover:opacity-85 transition-all duration-200 rounded-md">
    <?php if($canAddToCollection): ?>
    <button class="bg-transparent hover:bg-slate-50 hover:bg-opacity-10 border-white border rounded-full w-9 h-9 btn-collection" data-tooltip="#collection_tooltip_<?php echo e($item->id); ?>" data-tooltip-placement="left" data-media-type="<?php echo e($mediaType); ?>" onclick="event.stopPropagation();">
        <i class="ki-outline ki-add-folder text-xl text-white"></i>
    </button>
    <div class="tooltip transition-opacity duration-300" id="collection_tooltip_<?php echo e($item->id); ?>">
        Collection
    </div>
    <?php endif; ?>

    
    </div>

    <!-- Description (Moved up to avoid overlap with download button) -->
    <div class="z-1 absolute bottom-16 left-4 w-[80%] opacity-0 group-hover:opacity-100 transition-all duration-200">
        <span class="text-white text-sm line-clamp-3"><?php echo e($item->description); ?></span>
    </div>

    <!-- Download Button (Left Bottom Corner) -->
    <?php
        $canDownload = isset($explorePermissions['canDownload'][$mediaType]) ? $explorePermissions['canDownload'][$mediaType] : false;
    ?>
    <?php if($canDownload): ?>
    <div class="z-20 absolute bottom-4 left-4 opacity-0 group-hover:opacity-100 transition-all duration-200">
        <a href="/explore/download/<?php echo e($item->id); ?>" class="download-btn flex items-center justify-center bg-transparent hover:bg-slate-50 hover:bg-opacity-10 rounded-full p-2 border-white border" data-tooltip="#download_tooltip_<?php echo e($item->id); ?>" data-tooltip-placement="top" data-media-type="<?php echo e($mediaType); ?>" onclick="event.stopPropagation();">
            <i class="ki-filled ki-arrow-down text-xl text-white"></i>
            <span class="download-text text-white text-sm ml-1 hidden sm:inline-block">Download</span>
        </a>
        <div class="tooltip transition-opacity duration-300" id="download_tooltip_<?php echo e($item->id); ?>">
            Download
        </div>
    </div>
    <?php endif; ?>

    <!-- Delete Button -->
    <?php if($canDelete): ?>
    <div class="z-20 absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-200">
        <button type="button" class="flex items-center justify-center bg-transparent hover:bg-red-500 hover:bg-opacity-70 border-white border rounded-full w-9 h-9 btn-delete" data-id="<?php echo e($item->id); ?>" data-tooltip="#delete_tooltip_<?php echo e($item->id); ?>" data-tooltip-placement="left" data-media-type="<?php echo e($mediaType); ?>" onclick="event.stopPropagation(); deleteContent(event, '<?php echo e($item->id); ?>', '<?php echo e($mediaType); ?>')">
            <i class="ki-outline ki-trash text-xl text-white"></i>
        </button>
        <div class="tooltip transition-opacity duration-300" id="delete_tooltip_<?php echo e($item->id); ?>">
            Delete
        </div>
    </div>
    <?php endif; ?>

    <div class="w-full h-full flex justify-center items-center overflow-hidden">
        <?php if($item->media_type == "Audio"): ?>
        <img src="<?php echo e(asset('assets/thumbnail/thumbnail_audio.svg')); ?>" class="rounded-[3px] p-2 bg-white w-full">
        <?php elseif($item->media_type == "Document"): ?>
        <img src="<?php echo e(asset('assets/'.$item->thumbnail_path)); ?>" class="rounded-[3px] w-full">
        <?php else: ?>
        <img src="<?php echo e(Storage::url($item->thumbnail_path)); ?>" class="rounded-[3px] w-full">
        <?php endif; ?>
    </div>

    <div class="transition-all duration-200 opacity-0 group-hover:bg-zinc-800 group-hover:opacity-40 absolute top-0 left-0 w-full h-full rounded-[3px]"></div>
</div>
<?php echo $__env->make('pages.explore.detail', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php /**PATH C:\laragon\www\grafindo-media-library\resources\views/pages/explore/content.blade.php ENDPATH**/ ?>