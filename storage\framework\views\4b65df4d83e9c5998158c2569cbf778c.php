<div class="card">
  <div class="card-header">
    <h3 class="text-lg">Content Details</h3>
  </div>
  <div class="card-body bg-[--tw-navbar-bg] scrollable min-h-[70vh]">
    <form action="<?php echo e(url('submit-content/update')); ?>" method="POST" id="detail-content-form" enctype="multipart/form-data">
      <?php echo method_field('PUT'); ?>
      <?php echo csrf_field(); ?>

      <label for="filename" class="text-sm">Asset Id</label>
      <h3 class="mb-4 font-semibold" id="filename-draft"></h3>

      <div class="mb-4">
        <label for="description" class="text-sm mb-3">Deskripsi<span class="text-danger">*</span></label>
        <textarea class="textarea <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> ? border-danger : '' <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" type="text" id="description-draft" rows="3" name="description" required></textarea>
        <div class="flex justify-start w-full">
          <span id="description_warning" class="text-xs mt-1 text-red-600 hidden">Deskripsi minimal 2 kata</span>
        </div>
        <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
          <span class="text-danger text-sm"><?php echo e($message); ?></span>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
      </div>

      <div class="mb-4">
        <label for="keyword" class="text-sm mb-3">Kata Kunci<span class="text-danger">*</span></label>
        <div class="relative">
            <input type="text" id="keywordInput" class="input <?php $__errorArgs = ['keyword'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> ? border-danger : '' <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="Ketik kata kunci dan tekan Enter (pisahkan dengan koma)" autocomplete="off" required>
            <!-- Dropdown removed to disable autofill suggestions -->
        </div>
        <div class="flex justify-between w-full" id="key_span">
          <div class="flex items-center gap-2">
            <span id="key_warning" class="text-xs mt-1 text-red-600 hidden">Kata kunci minimal 5 kata</span>
            <span class="text-xs mt-1"><span id="keywords_counter">0</span>/20 Kata Kunci</span>
          </div>
        </div>
        <div id="keywordsContainerDraft" class="mt-2 flex flex-wrap gap-2"></div>
        <input type="hidden" name="keyword" id="hiddenKeywords">
        <?php $__errorArgs = ['keyword'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
          <span class="text-danger text-sm"><?php echo e($message); ?></span>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
      </div>

      <div class="mb-4">
        <label for="category" class="text-sm mb-3">Kategori 1 - Umum<span class="text-danger">*</span></label>
        <select class="select" id="category-draft" name="category_id">
          <option value="">Pilih Kategori</option>
          <?php $__currentLoopData = $general_categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
          <option value="<?php echo e($item->id); ?>"><?php echo e($item->name); ?></option>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
        <?php $__errorArgs = ['category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
          <span class="text-danger text-sm"><?php echo e($message); ?></span>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
      </div>

      <div class="mb-4">
        <label for="category_2" class="text-sm mb-3">Kategori 2 - Mata Pelajaran (Opsional)</label>
        <select class="select" id="category_2-draft" name="category_2_id">
          <option value="">Pilih Kategori</option>
          <?php $__currentLoopData = $subject_categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
          <option value="<?php echo e($item->id); ?>"><?php echo e($item->name); ?></option>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
        <?php $__errorArgs = ['category_2_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
          <span class="text-danger text-sm"><?php echo e($message); ?></span>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
      </div>

      <div class="mb-4">
        <label for="media-type" class="text-sm mb-3">Tipe Media<span class="text-danger">*</span></label>
        <select class="select" id="media_type-draft" name="media_type">
          <option value="Photo">Photo</option>
          <option value="Illustration">Illustration</option>
          <option value="Video">Video</option>
          <option value="Audio">Audio</option>
          <option value="Document">Document</option>
        </select>
        <?php $__errorArgs = ['media_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
          <span class="text-danger text-sm"><?php echo e($message); ?></span>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
      </div>

      <div class="mb-4">
        <label for="creator" class="text-sm mb-3">Kreator<span class="text-danger">*</span></label>
        <input type="text" class="input <?php $__errorArgs = ['creator_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> ? border-danger : '' <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="Masukan nama kreator" id="creator_name-draft" name="creator_name" required/>
        <?php $__errorArgs = ['creator_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
          <span class="text-danger text-sm"><?php echo e($message); ?></span>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
      </div>

      <div class="mb-4">
        <label for="date_taken" class="text-sm mb-3">Tanggal Pengambilan Konten<span class="text-danger">*</span></label>
        <div class="input-group">
          <input class="input <?php $__errorArgs = ['date_taken'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> ? border-danger : '' <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="" type="date" max="<?php echo e(date('Y-m-d')); ?>" id="date_taken-draft" name="date_taken" data-datepicker required/>
        </div>
        <?php $__errorArgs = ['date_taken'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
          <span class="text-danger text-sm"><?php echo e($message); ?></span>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
      </div>

      <div class="mb-4">
        <label for="release_document" class="text-sm mb-3">Dokumen Rilis (Opsional)</label>
        <div class="input-group mb-2">
          <input class="file-input <?php $__errorArgs = ['release_document'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> ? border-danger : '' <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="" accept=".pdf, .doc, .docx" type="file" id="release_document-draft" name="release_document" required/>
        </div>
        <span id="current_release_doc-draft" class="text-sm"></span>
        <?php $__errorArgs = ['release_document'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
          <span class="text-danger text-sm"><?php echo e($message); ?></span>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
      </div>

      <div id="group-input-content-id"></div>
      <input type="hidden" id="update-type" name="update_type">
    </form>
  </div>
</div>
<?php /**PATH C:\laragon\www\grafindo-media-library\resources\views/pages/submit-content/form.blade.php ENDPATH**/ ?>