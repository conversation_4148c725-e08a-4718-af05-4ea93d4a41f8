<?php $__env->startPush('toast-styles'); ?>
<style>
  body.no-scroll {
      overflow-x: hidden;
    }

  .toast {
    transform: translateX(calc(100% + 30px));
    transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.35);
    z-index: 100;
  }
  
  .toast.active {
    transform: translateX(0%)
  }
    
  .progress-bar {
    --progress-color: var(--success);
  }

  .progress-bar::before{
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background-color: var(--progress-color);
  }

  .progress-bar.active::before{
    animation: progress 5s linear forwards;
  }

  @keyframes progress {
    100%{
      right: 100%;
    }
  }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('toast'); ?>
  <div class="toast fixed w-80 top-4 right-4 bg-white py-5 pr-9 pl-6 rounded-lg shadow-lg border-l-[5px] overflow-hidden">
    <div class="flex items-center gap-2.5">
      <div class="toast-side flex items-center justify-center w-7 h-7 text-white text-xl rounded-full">
        <i class="toast-icon text-sm"></i>
      </div>
      <div class="grow">
        <span class="toast-title text-sm font-bold text-neutral-700"></span>
        <p class="toast-subtitle text-xs font-normal text-neutral-500"></p>
      </div>
      <i class="btn-close fa-solid fa-x text-[10px] font-medium absolute top-1.75 right-3 p-1 cursor-pointer text-neutral-400 hover:text-neutral-800"></i>
      <div class="progress-bar absolute left-0 bottom-0 w-full h-1 bg-white before:absolute"></div>
    </div>
  </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('toast-scripts'); ?>
<?php if(session('type') && session('message')): ?>
<script>
  $(document).ready(function() {
    myToast("<?php echo e(session('type')); ?>", "<?php echo e(session('message')); ?>");
  });
</script>
<?php endif; ?>
<?php $__env->stopPush(); ?><?php /**PATH C:\laragon\www\grafindo-media-library\resources\views/components/toast.blade.php ENDPATH**/ ?>