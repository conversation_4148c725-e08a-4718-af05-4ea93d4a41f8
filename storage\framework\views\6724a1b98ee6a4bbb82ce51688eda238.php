<script>
  // This script uses global variables defined in main-scripts.blade.php
  $(document).off("click", ".btn-review-history").on("click", ".btn-review-history", function (e) {
      let cardId = $(this).data("id");
      let accordionContent = $("#accordion-"+cardId);
      let loading = $(".loading");
      accordionContent.empty()

      if(accordionContent[0].classList.length == 2){
        accordionContent.removeClass("hidden")
        $("#plus-accordion-"+cardId).addClass("hidden")
        $("#minus-accordion-"+cardId).removeClass("hidden")
        $.ajax({
          url: "submit-content/get-reviews/"+cardId,
          method: "GET",
          beforeSend: function() {
            loading.show();
          },
          success: function(res) {
            let data = res.data;
            data.forEach(item => {
              let badgeStatus;

              switch (item.status){
                case 'Approved': badgeStatus = 'badge-primary'; break;
                case 'Refused': badgeStatus = 'badge-danger'; break;
                case 'Publish': badgeStatus = 'badge-success'; break;
              }

              accordionContent.append(`
                <div class="card p-4 mb-2 text-gray-700 text-md pb-4 relative">
                  <h6 class="font-bold">${item.reviewer.name}</h6>
                  <span class="text-xs">${item.review_date}</span>
                  <p class="text-sm mt-2">Note: ${item.comments ?? '-'}</p>
                  <span class="badge badge-outline ${badgeStatus} absolute top-3 right-3 rounded-full">${item.status}</span>
                </div>
              `);
            })
            loading.hide();
          },
          error: function(err) {
            let res = err.responseJSON;
          }
        });
      }else{
        accordionContent.addClass("hidden")
        $("#plus-accordion-"+cardId).removeClass("hidden")
        $("#minus-accordion-"+cardId).addClass("hidden")
        loading.hide();
      }
    });
</script>
<?php /**PATH C:\laragon\www\grafindo-media-library\resources\views/pages/submit-content/components/review-scripts.blade.php ENDPATH**/ ?>