<!-- Header -->
<header class="flex items-center transition-[height] shrink-0 bg-[#f5f7fa] text-gray-700 h-[--tw-header-height] border-b border-gray-300" data-sticky="true" data-sticky-class="transition-[height] fixed z-50 top-0 left-0 right-0 shadow-sm backdrop-blur-md bg-[#f5f7fa]/90 border-b border-gray-300" data-sticky-name="header" data-sticky-offset="0px" id="header">
  <!-- Container -->
  <div class="container-fixed flex lg:justify-between items-center gap-2.5">
   <!-- Mobile Menu Toggle -->
   <div class="flex items-center gap-1">
    <button class="btn btn-icon text-gray-700 hover:bg-gray-100 btn-sm -ms-2.5 lg:hidden" data-drawer-toggle="#sidebar">
     <i class="ki-filled ki-menu">
     </i>
    </button>
    <!-- Page Title (Mobile Only) -->
    <div class="flex items-center gap-2 lg:hidden">
     <h3 class="text-gray-800 text-lg font-medium">
      Grafindo Media Library
     </h3>
    </div>
   </div>
   <!-- End of Mobile Menu Toggle -->
   <!-- Topbar -->
   <div class="flex items-center gap-2 lg:gap-3.5 lg:w-[400px] justify-end">
    <div class="flex items-center gap-2">
     <div class="dropdown" data-dropdown="true" data-dropdown-offset="-7px, 10px" data-dropdown-offset-rtl="7px, 10px" data-dropdown-placement="bottom-end" data-dropdown-placement-rtl="bottom-start" data-dropdown-trigger="click|lg:click">
      <button id="btn-notification" class="dropdown-toggle relative btn btn-icon btn-icon-base btn-sm text-gray-700 hover:text-gray-900 dropdown-open:text-gray-900">
      <span class="badge badge-dot hidden badge-danger size-[6px] absolute top-0.5 right-3 transform translate-y-1/2 translate-x-full">
      </span>
       <i class="ki-filled ki-notification">
       </i>
      </button>
      <div class="dropdown-content light:border-gray-300 w-full max-w-[460px]">
       <div class="flex items-center justify-between gap-2.5 text-sm text-gray-900 font-semibold px-5 py-2.5 border-b border-b-gray-200" id="notifications_header">
        Notifications
        <button class="btn btn-sm btn-icon btn-light btn-clear shrink-0" data-dropdown-dismiss="true">
         <i class="ki-filled ki-cross">
         </i>
        </button>
       </div>
       <div class="grow" id="notifications_tab_all">
        <div class="flex flex-col">
         <div class="scrollable-y-auto" data-scrollable="true" data-scrollable-dependencies="#header" data-scrollable-max-height="auto" data-scrollable-offset="200px">
           <div class="text-center loading mt-2 hidden">
             <div role="status">
               <svg aria-hidden="true" class="inline w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-gray-600 dark:fill-gray-300" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                   <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
                   <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/>
               </svg>
               <span class="sr-only">Loading...</span>
             </div>
           </div>
          <div class="flex flex-col pt-3 pb-4 divider-y divider-gray-200" id="notif_container">
            

           
          </div>
         </div>
         <div class="border-b border-b-gray-200">
         </div>
         <div class="hidden" id="btn-mark">
           <div class="grid p-5 gap-2.5" >
            <button class="btn btn-sm btn-light justify-center" id="btn-read-all">
             Mark all as read
            </button>
           </div>
         </div>
        </div>
       </div>
      </div>
     </div>
     <div class="menu" data-menu="true">
      <div class="menu-item" data-menu-item-offset="-7px, 10px" data-menu-item-offset-rtl="7px, 10px" data-menu-item-placement="bottom-end" data-menu-item-placement-rtl="bottom-start" data-menu-item-toggle="dropdown" data-menu-item-trigger="click|lg:click">
       <button class="menu-toggle btn btn-icon rounded-full">
        <?php
          $defaultAvatarUrl = asset('assets/media/avatars/blank.png');
          $avatarUrl = auth()->user()->avatar
              ? Storage::url(auth()->user()->avatar)
              : $defaultAvatarUrl;
        ?>
        <img alt="" class="size-9 rounded-full border-2 border-white" src="<?php echo e($avatarUrl); ?>">
       </button>
       <div class="menu-dropdown menu-default light:border-gray-300 w-screen max-w-[250px]">
        <div class="flex items-center justify-between px-5 py-1.5 gap-1.5">
         <div class="flex items-center gap-2">
          <img alt="" class="size-14 rounded-full border-2 border-white" src="<?php echo e($avatarUrl); ?>">
           <div class="flex flex-col gap-1.5">
            
            <a class="text-xs text-gray-600 hover:text-primary font-medium leading-none" href="html/demo9/account/home/<USER>">
             <?php echo e(auth()->user()->username); ?>

            </a>
            
           </div>
          </img>
         </div>
        </div>
        <div class="menu-separator">
        </div>
        <div class="flex flex-col">
         <?php if(auth()->user()->hasPermissions('works.profiles', auth()->user()->role_id)): ?>
         <div class="menu-item">
          <a class="menu-link" href="<?php echo e(url('/profiles?tab=works')); ?>">
           <span class="menu-icon">
            <i class="ki-filled ki-briefcase">
            </i>
           </span>
           <span class="menu-title">
            My Works
           </span>
          </a>
         </div>
         <?php endif; ?>
         
         <?php if(auth()->user()->hasPermissions('collection.profiles', auth()->user()->role_id)): ?>
         <div class="menu-item">
          <a class="menu-link" href="<?php echo e(url('/profiles?tab=collections')); ?>">
           <span class="menu-icon">
            <i class="ki-filled ki-like-folder">
            </i>
           </span>
           <span class="menu-title">
            My Collections
           </span>
          </a>
         </div>
         <?php endif; ?>
         

        </div>
        <div class="menu-separator">
        </div>
        <div class="flex flex-col">
         <div class="menu-item mb-0.5">
          <div class="menu-link">
           <span class="menu-icon">
            <i class="ki-filled ki-moon">
            </i>
           </span>
           <span class="menu-title">
            Dark Mode
           </span>
           <label class="switch switch-sm">
            <input data-theme-state="dark" data-theme-toggle="true" name="check" type="checkbox" value="1"/>
           </label>
          </div>
         </div>
         <div class="menu-item px-4 py-1.5">
          <form action="<?php echo e(route('logout')); ?>" method="POST">
            <?php echo csrf_field(); ?>
            <button class="btn btn-sm btn-light justify-center w-full" type="submit">Log out</button>
          </form>
         </div>
        </div>
       </div>
      </div>
     </div>
    </div>
   </div>
   <!-- End of Topbar -->
  </div>
  <!-- End of Container -->
 </header>
 <!-- End of Header -->

<?php $__env->startPush('styles'); ?>
<style>
  /* Add padding to main content to prevent it from being hidden behind sticky header */
  body.header-is-sticky .app-content {
    padding-top: var(--tw-header-height, 70px);
  }

  /* Ensure content doesn't jump when header becomes sticky */
  .app-content {
    position: relative;
    z-index: 1;
    margin-left: 250px; /* Match sidebar width */
  }

  /* Adjust for mobile view */
  @media (max-width: 1023px) {
    .app-content {
      margin-left: 0;
    }
  }

  /* Make sure the header is always on top but doesn't cover sidebar */
  #header.fixed {
    z-index: 40 !important; /* Lower than sidebar z-index (50) */
    width: calc(100% - 250px) !important; /* Adjust width to not cover sidebar */
    left: 250px !important; /* Adjust left position to align with sidebar width */
    margin-left: 0 !important;
  }

  /* Adjust for mobile view */
  @media (max-width: 1023px) {
    #header.fixed {
      width: 100% !important;
      left: 0 !important;
    }
  }

  /* Add smooth transition for sticky header */
  #header {
    transition: transform 0.3s ease, background-color 0.3s ease;
  }

  /* Add border to header in both states */
  #header {
    border-bottom: 1px solid #d1d5db !important; /* Match sidebar border color (gray-300) */
  }

  /* Optional: Add shadow when header is sticky */
  #header.fixed {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid #d1d5db !important; /* Match sidebar border color (gray-300) */
  }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
  $(document).ready(function() {
    // Sticky Header Implementation
    const header = document.getElementById('header');
    const stickyClass = header.getAttribute('data-sticky-class');
    const stickyOffset = header.getAttribute('data-sticky-offset') || '0px';
    const offsetValue = parseInt(stickyOffset) || 0;

    function handleScroll() {
      if (window.scrollY > offsetValue) {
        // Add sticky classes
        header.classList.add(...stickyClass.split(' '));
        header.classList.add('fixed');

        // Add class to body to adjust content
        document.body.classList.add('header-is-sticky');
      } else {
        // Remove sticky classes
        header.classList.remove(...stickyClass.split(' '));
        header.classList.remove('fixed');

        // Remove class from body
        document.body.classList.remove('header-is-sticky');
      }
    }

    // Initial check
    handleScroll();

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll);

    const badge = $(".badge-dot");

    getNotification();

    function readNotification(notifId) {
      $.ajax({
        url: "<?php echo e(route('read-notif')); ?>",
        method: "PUT",
        data: {
          '_token': "<?php echo e(csrf_token()); ?>",
          'id': notifId,
        },
        success: function(res) {
          if(res){
            getNotification();
          }
        },
        error: function(err) {
          let res = err.responseJSON
          console.log(res)
        }
      })
    }

    function readAllNotification() {
      $.ajax({
        url: "<?php echo e(route('read-all-notif')); ?>",
        method: "GET",
        success: function(res) {
          if(res){
            getNotification();
          }
        },
        error: function(err) {
          let res = err.responseJSON
          console.log(res)
        }
      })
    }

    function getNotification() {
      $.ajax({
        url: "<?php echo e(route('get-notif')); ?>",
        method: "GET",
        beforeSend: function(){
          $(".loading").show();
        },
        success: function(res){
          loadNotification(res);
        },
        error: function(){
          let res = err.responseJSON
          console.log(res)
        },
      })
    }

    function loadNotification(res) {
      const container = $("#notif_container");
      const btnMark = $("#btn-mark");

      container.empty()

      if(res.data.length !== 0){
        res.data.forEach((item, index, array) => {
          const defaultAvatarUrl = "<?php echo e(url('assets/media/avatars/blank.png')); ?>";
          const avatar = item.user.avatar ? `<?php echo e(Storage::url('${item.user.avatar}')); ?>` : defaultAvatarUrl;
          const time = formatDate(item.created_at);
          let textColor;

          switch (item.type.toLowerCase()) {
            case "publish":
                textColor = "text-success";
                break;
            case "reject":
            case "refuse":
                textColor = "text-danger";
                break;
            case "approved":
                textColor = "text-primary";
                break;
            case "submit":
                textColor = "text-warning";
                break;
            default:
                textColor = "text-gray-500";
                break;
          }

          let notif = `
            <div class="flex grow gap-3 px-5 ${item.is_read === 0 ? 'hover:bg-gray-100' : 'bg-gray-100'} cursor-pointer h-16 msg" data-id="${item.id}">
              <div class="relative flex items-center shrink-0 mt-0.5">
               <img alt="" class="rounded-full size-8" src="${avatar}"/>
              </div>
              <div class="flex justify-center flex-col gap-1">
               <div class="text-2sm font-medium mb-px">
                <span class="text-gray-900 font-semibold">
                 ${item.user.name}
                </span>
                <span class="text-gray-700">
                 ${item.message}
                </span>
               </div>
               <span class="flex items-center text-2xs font-medium text-gray-500">
                ${time}
                <span class="badge badge-circle bg-gray-500 size-1 mx-1.5">
                </span>
                <span class="${textColor}">${item.type}</span>
               </span>
              </div>
            </div>
            <div class="border-b border-b-gray-200"></div>
          `
          container.append(notif)
          badge.show()
          btnMark.show()
        });
      }else{
        container.html(`
          <div class="flex justify-center grow gap-2.5 mb-3 px-5">
            <div class="relative shrink-0 mt-0.5">
              <span class="text-gray-700 text-sm">Tidak ada notifikasi</span>
            </div>
          </div>
          <div class="border-b border-b-gray-200"></div>
        `)
        badge.hide();
        btnMark.hide();
      }

      $(".loading").hide();
    }

    function formatDate(dateString) {
      let date = new Date(dateString);
      let now = new Date();

      let diffInMs = now - date;
      let diffInMinutes = Math.floor(diffInMs / (1000 * 60));
      let diffInHours = Math.floor(diffInMinutes / 60);
      let diffInDays = Math.floor(diffInHours / 24);

      if (diffInMinutes < 60) {
          return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
      } else if (diffInHours < 24) {
          return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
      } else if (diffInDays < 30) {
          return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
      } else {
          return "Long time ago";
      }
    }

    $("#btn-notification").on("click", function() {
      getNotification();
    })

    $(document).on("click", ".msg", function() {
      const notifId = $(this).data('id');
      readNotification(notifId);
    })

    $(document).on("click", "#btn-mark", function() {
      readAllNotification();
    })

  })
</script>
<?php $__env->stopPush(); ?><?php /**PATH C:\laragon\www\grafindo-media-library\resources\views/layouts/partials/header.blade.php ENDPATH**/ ?>